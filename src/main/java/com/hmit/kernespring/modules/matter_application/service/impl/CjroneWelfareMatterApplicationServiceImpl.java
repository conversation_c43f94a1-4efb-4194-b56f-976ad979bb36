package com.hmit.kernespring.modules.matter_application.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.DateUtils;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.common.utils.ShiroUtils;
import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.cjrone.entity.*;
import com.hmit.kernespring.modules.cjrone.service.*;
import com.hmit.kernespring.modules.cjrone_bl.entity.*;
import com.hmit.kernespring.modules.cjrone_bl.service.*;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.service.DataChildEducationSubsidyService;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import com.hmit.kernespring.modules.data_management.service.DataResidentPensionInsuranceService;
import com.hmit.kernespring.modules.data_management.service.DisabilityCertificateSyncDataService;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;
import com.hmit.kernespring.modules.matter_application.entity.CjroneMentalIllnessSubsidyEntity;
import com.hmit.kernespring.modules.matter_application.entity.CjroneWelfareMatterApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.CjroneWelfareMatterApplicationService;
import com.hmit.kernespring.modules.matter_application.service.DisabilityCertificateApplicationService;
import com.hmit.kernespring.modules.sys.entity.SysUserEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Optional;


/**
 * <AUTHOR>
 */
@Slf4j
@Service("cjroneWelfareMatterApplicationService")
public class CjroneWelfareMatterApplicationServiceImpl extends ServiceImpl<CjroneWelfareMatterApplicationDao, CjroneWelfareMatterApplicationEntity> implements CjroneWelfareMatterApplicationService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
                    .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

                        @Override
                        public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                            if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                                //定义为int类型,如果后台返回""或者null,则返回0
                                return null;
                            }
                            return json.getAsInt();
                        }
                    })
                    .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

                        @Override
                        public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                            if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                                //定义为int类型,如果后台返回""或者null,则返回0
                                return null;
                            }
                            return json.getAsInt();
                        }
                    })
                    .create();


    @Autowired
    private CjroneWelfareMatterApplicationDao cjroneWelfareMatterApplicationDao;
    @Autowired
    private DisabilityCertificateSyncDataService disabilityCertificateSyncDataService;
    @Autowired
    private SysDictService sysDictService;
    @Autowired
    private CjroneWelfareApplyDocService cjroneWelfareApplyDocService;
    @Autowired
    private CjroneLivingAllowanceService cjroneLivingAllowanceService;
    @Autowired
    private CjroneNursingSubsidyService cjroneNursingSubsidyService;
    @Autowired
    private CjroneRehabilitationSubsidyService cjroneRehabilitationSubsidyService;
    @Autowired
    private CjroneResidentPensionInsuranceService cjroneResidentPensionInsuranceService;
    @Autowired
    private DataResidentPensionInsuranceService dataResidentPensionInsuranceService;
    @Autowired
    private CjroneEmploymentSubsidyService cjroneEmploymentSubsidyService;
    @Autowired
    private DataChildEducationSubsidyService dataChildEducationSubsidyService;
    @Autowired
    private CjroneComprehensiveMedicalInsuranceService cjroneComprehensiveMedicalInsuranceService;
    @Autowired
    private CjroneFamilyAccessibilityTransformationService cjroneFamilyAccessibilityTransformationService;
    @Autowired
    private APIService apiService;
    @Autowired
    private DisabilityCertificateApplicationService disabilityCertificateApplicationService;
    @Autowired
    private CjroneTwoSubsidyStandardsService cjroneTwoSubsidyStandardsService;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;
    @Autowired
    private CjroneblLivingSubsidyService cjroneblLivingSubsidyService;
    @Autowired
    private CjroneblDisabilityCertificateRedemptionService cjroneblDisabilityCertificateRedemptionService;
    @Autowired
    private CjroneblNursingSubsidyService cjroneblNursingSubsidyService;
    @Autowired
    private CjroneblLivingAllowanceService cjroneblLivingAllowanceService;
    @Autowired
    private CjroneblZgjbyanglaoService cjroneblZgjbyanglaoService;
    @Autowired
    private CjroneblZgjbyiliaoService cjroneblZgjbyiliaoService;
    @Autowired
    private CjroneblCxjmyanglaoService cjroneblCxjmyanglaoService;
    @Autowired
    private CjroneblCxjmyiliaoService cjroneblCxjmyiliaoService;
    @Autowired
    private CjroneblTemporaryAssistanceService cjroneblTemporaryAssistanceService;
    @Autowired
    private CjroneblRehabilitationSubsidyService cjroneblRehabilitationSubsidyService;
    @Autowired
    private CjroneblBusinessGrantService cjroneblBusinessGrantService;
    @Autowired
    private CjroneblCollegeeduService cjroneblCollegeeduService;
    @Autowired
    private CjroneblChildeduService cjroneblChildeduService;
    @Autowired
    private CjroneblMedicalSupportService cjroneblMedicalSupportService;
    @Autowired
    private CjroneblHospitalizationAllowanceService cjroneblHospitalizationAllowanceService;
    @Autowired
    private Love24Service love24Service;
    @Autowired
    private CjroneChildrenRehabilitationSubsidyService cjroneChildrenRehabilitationSubsidyService;
    @Autowired
    private CjroneMentalIllnessSubsidyServiceImpl cjroneMentalIllnessSubsidyService;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        String type = params.get("type") !=null && !"".equals(params.get("type").toString()) ? params.get("type").toString() : null;
        String usertype=params.get("usertype") !=null && !"".equals(params.get("usertype").toString()) ? params.get("usertype").toString() : null;  //用户类型
        CjroneWelfareMatterApplicationEntity cjroneWelfareMatterApplicationEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneWelfareMatterApplicationEntity.class);

        List<String> list = new ArrayList<>();
        //如果是村社区，需要看 退回状态为9的记录
        if (type != null && "1".equals(usertype)){
            // 加入退回的状态
            list.add("9");
        }
        //街道
        if (type != null && "2".equals(usertype)){
            // 加入退回的状态
            list.add("12");
        }
        System.out.println("type"+type);
        if (cjroneWelfareMatterApplicationEntity.getStatus () != null && !"".equals(cjroneWelfareMatterApplicationEntity.getStatus ().toString())){
            list.add(cjroneWelfareMatterApplicationEntity.getStatus ());
        }
        String startDay = cjroneWelfareMatterApplicationEntity.getStartDay ()!=null && !"".equals(cjroneWelfareMatterApplicationEntity.getStartDay ().toString())? cjroneWelfareMatterApplicationEntity.getStartDay ().toString():null;
        String endDay = cjroneWelfareMatterApplicationEntity.getEndDay ()!=null && !"".equals(cjroneWelfareMatterApplicationEntity.getEndDay ().toString())? cjroneWelfareMatterApplicationEntity.getEndDay ().toString():null;

        List<String> listFinal = list;
        IPage<CjroneWelfareMatterApplicationEntity> page = this.page(
                new Query<CjroneWelfareMatterApplicationEntity>().getPage(params),
                new QueryWrapper<CjroneWelfareMatterApplicationEntity>()
            .eq(StringUtils.isNotBlank(cjroneWelfareMatterApplicationEntity.getId ()!=null && !"".equals(cjroneWelfareMatterApplicationEntity.getId ().toString())? cjroneWelfareMatterApplicationEntity.getId ().toString():null),"id", cjroneWelfareMatterApplicationEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneWelfareMatterApplicationEntity.getName ()!=null && !"".equals(cjroneWelfareMatterApplicationEntity.getName ().toString())? cjroneWelfareMatterApplicationEntity.getName ().toString():null),"name", cjroneWelfareMatterApplicationEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneWelfareMatterApplicationEntity.getIdCard ()!=null && !"".equals(cjroneWelfareMatterApplicationEntity.getIdCard ().toString())? cjroneWelfareMatterApplicationEntity.getIdCard ().toString():null),"id_card", cjroneWelfareMatterApplicationEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(cjroneWelfareMatterApplicationEntity.getMobilePhone ()!=null && !"".equals(cjroneWelfareMatterApplicationEntity.getMobilePhone ().toString())? cjroneWelfareMatterApplicationEntity.getMobilePhone ().toString():null),"mobile_phone", cjroneWelfareMatterApplicationEntity.getMobilePhone ())
            .eq(StringUtils.isNotBlank(cjroneWelfareMatterApplicationEntity.getDisableId ()!=null && !"".equals(cjroneWelfareMatterApplicationEntity.getDisableId ().toString())? cjroneWelfareMatterApplicationEntity.getDisableId ().toString():null),"disable_id", cjroneWelfareMatterApplicationEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(cjroneWelfareMatterApplicationEntity.getMatterName ()!=null && !"".equals(cjroneWelfareMatterApplicationEntity.getMatterName ().toString())? cjroneWelfareMatterApplicationEntity.getMatterName ().toString():null),"matter_name", cjroneWelfareMatterApplicationEntity.getMatterName ())
            .eq(StringUtils.isNotBlank(cjroneWelfareMatterApplicationEntity.getApplicationTime ()!=null && !"".equals(cjroneWelfareMatterApplicationEntity.getApplicationTime ().toString())? cjroneWelfareMatterApplicationEntity.getApplicationTime ().toString():null),"application_time", cjroneWelfareMatterApplicationEntity.getApplicationTime ())
            .eq(StringUtils.isNotBlank(cjroneWelfareMatterApplicationEntity.getVerifyTime ()!=null && !"".equals(cjroneWelfareMatterApplicationEntity.getVerifyTime ().toString())? cjroneWelfareMatterApplicationEntity.getVerifyTime ().toString():null),"verify_time", cjroneWelfareMatterApplicationEntity.getVerifyTime ())
            .eq(StringUtils.isNotBlank(cjroneWelfareMatterApplicationEntity.getStatus ()!=null && type == null && !"".equals(cjroneWelfareMatterApplicationEntity.getStatus ().toString())? cjroneWelfareMatterApplicationEntity.getStatus().toString():null),"status", cjroneWelfareMatterApplicationEntity.getStatus ())
            .and(type != null,wrapper -> wrapper.in("status",listFinal))
            .between(StringUtils.isNotBlank(startDay) && StringUtils.isNotBlank(endDay),"create_time",startDay,endDay)
            .eq(StringUtils.isNotBlank(cjroneWelfareMatterApplicationEntity.getNativeCun ()!=null && !"".equals(cjroneWelfareMatterApplicationEntity.getNativeCun ().toString())? cjroneWelfareMatterApplicationEntity.getNativeCun().toString():null),"native_cun", cjroneWelfareMatterApplicationEntity.getNativeCun ())
            .eq(StringUtils.isNotBlank(cjroneWelfareMatterApplicationEntity.getNativeZhen ()!=null && !"".equals(cjroneWelfareMatterApplicationEntity.getNativeZhen ().toString())? cjroneWelfareMatterApplicationEntity.getNativeZhen().toString():null),"native_zhen", cjroneWelfareMatterApplicationEntity.getNativeZhen ())
            .orderByDesc("create_time")
        );

        page.getRecords().forEach( item -> {

            if ("1".equals(item.getStatus())){
                item.setStatus("申请人待手签");
            }else if ("2".equals(item.getStatus())){
                item.setStatus("街道待审核");
            }else if ("4".equals(item.getStatus()) || "5".equals(item.getStatus()) ){
                item.setStatus("民政局待审核");
            }else if ("6".equals(item.getStatus()) || "7".equals(item.getStatus()) ){
                item.setStatus("区残联待审核");
            }else if ("8".equals(item.getStatus())){
                item.setStatus("通过");
            }else if ("0".equals(item.getStatus())){
                item.setStatus("禁用");
            }else if("9".equals(item.getStatus())){
                item.setStatus("街道退回");
            }else if("12".equals(item.getStatus())){
                item.setStatus("区残联退回");
            }

           //根据不同的角色，对签字和盖章状态进行区分
            if("1".equals(usertype)){
                // 村社区角色
                if ("1".equals(item.getSignStatus())) {
                    item.setSignStatus("申请人待手签");
                }
                if ("1".equals(item.getSignatureStatus())) {
                    item.setSignatureStatus("无");
                }
            } else if("2".equals(usertype)){
                // 镇街道角色
                if ("2".equals(item.getSignStatus())) {
                    item.setSignStatus("街道待手签");
                }
                if("4".equals(item.getSignStatus())||"6".equals(item.getSignStatus())){
                    item.setSignStatus("街道已手签");
                }
                if ("2".equals(item.getSignatureStatus())) {
                    item.setSignatureStatus("街道待电子签章");
                }
                if("4".equals(item.getSignatureStatus())||"5".equals(item.getSignatureStatus())){
                    item.setSignatureStatus("街道已电子签章");
                }
            }

            if("1".equals(item.getSignStatus())){
                item.setSignStatus("申请人待手签");
            }else if("2".equals(item.getSignStatus())){
                item.setSignStatus("镇街道待手签");
            }else if("4".equals(item.getSignStatus())||"5".equals(item.getSignStatus())){
                item.setSignStatus("民政待手签");
            }else if("6".equals(item.getSignStatus())||"7".equals(item.getSignStatus())){
                item.setSignStatus("区残联待手签");
            }else if("8".equals(item.getSignStatus())){
                item.setSignStatus("完成手签");
            }
            if("1".equals(item.getSignatureStatus())){
                item.setSignatureStatus("无");
            }else if("2".equals(item.getSignatureStatus())){
                item.setSignatureStatus("镇街道待电子签章");
            }else if("4".equals(item.getSignatureStatus())){
                item.setSignatureStatus("民政待电子签章");
            }else if("5".equals(item.getSignatureStatus())){
                item.setSignatureStatus("区残联待电子签章");
            }else if("6".equals(item.getSignatureStatus())){
                item.setSignatureStatus("完成电子签章");
            }

        });
        return new PageUtils(page);
    }


    @Override
    public List<CjroneWelfareMatterApplicationEntity> queryExportData(Map<String, Object> params) {
            return cjroneWelfareMatterApplicationDao.queryExportData(params);
    }

    @Override
    public Map<String, Object> queryMattersByMap(Map<String, Object> params) {
        return cjroneWelfareMatterApplicationDao.queryMattersByMap(params);
    }

    @Override
    public Map<String, Object> queryMattersIdByMap(Map<String, Object> params) {
        return cjroneWelfareMatterApplicationDao.queryMattersIdByMap(params);

    }

    @Override
    public void updateStatusById(CjroneWelfareMatterApplicationEntity welfareMatterApplicationEntity) {
        cjroneWelfareMatterApplicationDao.updateStatusById(welfareMatterApplicationEntity);
    }
    @Override
    @Transactional(rollbackFor=Exception.class)
    public void updateSignStatusByMap(CjroneWelfareMatterApplicationEntity welfareMatterApplicationEntity) {

        String signType = welfareMatterApplicationEntity.getMatterName();
        if (signType.indexOf("生活补贴") != -1){
            // 更新生活补贴实体的各种状态
            CjroneblLivingSubsidyEntity cjroneblLivingSubsidyEntity=new CjroneblLivingSubsidyEntity();
            cjroneblLivingSubsidyEntity.setId(welfareMatterApplicationEntity.getMatterId());
            cjroneblLivingSubsidyEntity.setStatus(welfareMatterApplicationEntity.getStatus());
            cjroneblLivingSubsidyEntity.setSignStatus(welfareMatterApplicationEntity.getSignStatus());
            cjroneblLivingSubsidyEntity.setSignatureStatus(welfareMatterApplicationEntity.getSignatureStatus());
            cjroneblLivingSubsidyService.updateByIdSign(cjroneblLivingSubsidyEntity);

        } else if (signType.indexOf("护理补贴") != -1){
            // 更新生活补贴实体的各种状态
            CjroneblNursingSubsidyEntity cjroneblNursingSubsidyEntity = new CjroneblNursingSubsidyEntity();
            cjroneblNursingSubsidyEntity.setId(welfareMatterApplicationEntity.getMatterId());
            cjroneblNursingSubsidyEntity.setStatus(welfareMatterApplicationEntity.getStatus());
            cjroneblNursingSubsidyEntity.setSignStatus(welfareMatterApplicationEntity.getSignStatus());
            cjroneblNursingSubsidyEntity.setSignatureStatus(welfareMatterApplicationEntity.getSignatureStatus());
            cjroneblNursingSubsidyService.updateByIdSign(cjroneblNursingSubsidyEntity);
        } else if(signType.indexOf("生活补助金") != -1){
            //更新生活补助金实体的各种状态
            CjroneblLivingAllowanceEntity cjroneblLivingAllowanceEntity =new CjroneblLivingAllowanceEntity();
            cjroneblLivingAllowanceEntity.setId(welfareMatterApplicationEntity.getMatterId());
            cjroneblLivingAllowanceEntity.setStatus(welfareMatterApplicationEntity.getStatus());
            cjroneblLivingAllowanceEntity.setSignStatus(welfareMatterApplicationEntity.getSignStatus());
            cjroneblLivingAllowanceEntity.setSignatureStatus(welfareMatterApplicationEntity.getSignatureStatus());
            cjroneblLivingAllowanceService.updateByIdSign(cjroneblLivingAllowanceEntity);
        }else if(signType.indexOf("职工基本养老保险补助") != -1){
            //更新职工基本养老保险实体的各种状态
            CjroneblZgjbyanglaoEntity cjroneblZgjbyanglaoEntity=new CjroneblZgjbyanglaoEntity();
            cjroneblZgjbyanglaoEntity.setId(welfareMatterApplicationEntity.getMatterId());
            cjroneblZgjbyanglaoEntity.setStatus(welfareMatterApplicationEntity.getStatus());
            cjroneblZgjbyanglaoEntity.setSignStatus(welfareMatterApplicationEntity.getSignStatus());
            cjroneblZgjbyanglaoEntity.setSignatureStatus(welfareMatterApplicationEntity.getSignatureStatus());
            cjroneblZgjbyanglaoService.updateByIdSign(cjroneblZgjbyanglaoEntity);
        }else if(signType.indexOf("职工基本医疗保险补助") != -1){
            //更新职工基本医疗保险实体的各种状态
            CjroneblZgjbyiliaoEntity cjroneblZgjbyanglaoEntity=new CjroneblZgjbyiliaoEntity();
            cjroneblZgjbyanglaoEntity.setId(welfareMatterApplicationEntity.getMatterId());
            cjroneblZgjbyanglaoEntity.setStatus(welfareMatterApplicationEntity.getStatus());
            cjroneblZgjbyanglaoEntity.setSignStatus(welfareMatterApplicationEntity.getSignStatus());
            cjroneblZgjbyanglaoEntity.setSignatureStatus(welfareMatterApplicationEntity.getSignatureStatus());
            cjroneblZgjbyiliaoService.updateByIdSign(cjroneblZgjbyanglaoEntity);
        }else if(signType.indexOf("城乡居民养老保险补助") != -1){
            //更新城乡居民养老保险实体的各种状态
            CjroneblCxjmyanglaoEntity cjroneblCxjmyanglaoEntity=new CjroneblCxjmyanglaoEntity();
            cjroneblCxjmyanglaoEntity.setId(welfareMatterApplicationEntity.getMatterId());
            cjroneblCxjmyanglaoEntity.setStatus(welfareMatterApplicationEntity.getStatus());
            cjroneblCxjmyanglaoEntity.setSignStatus(welfareMatterApplicationEntity.getSignStatus());
            cjroneblCxjmyanglaoEntity.setSignatureStatus(welfareMatterApplicationEntity.getSignatureStatus());
            cjroneblCxjmyanglaoService.updateByIdSign(cjroneblCxjmyanglaoEntity);
        }else if(signType.indexOf("城乡基本医疗保险补助") != -1){
            //更新城乡居民养老保险实体的各种状态
            CjroneblCxjmyiliaoEntity cjroneblCxjmyiliaoEntity=new CjroneblCxjmyiliaoEntity();
            cjroneblCxjmyiliaoEntity.setId(welfareMatterApplicationEntity.getMatterId());
            cjroneblCxjmyiliaoEntity.setStatus(welfareMatterApplicationEntity.getStatus());
            cjroneblCxjmyiliaoEntity.setSignStatus(welfareMatterApplicationEntity.getSignStatus());
            cjroneblCxjmyiliaoEntity.setSignatureStatus(welfareMatterApplicationEntity.getSignatureStatus());
            cjroneblCxjmyiliaoService.updateByIdSign(cjroneblCxjmyiliaoEntity);
        }else if(signType.indexOf("残疾人临时救助") != -1){
            //更新城乡居民养老保险实体的各种状态
            CjroneblTemporaryAssistanceEntity cjroneblTemporaryAssistanceEntity=new CjroneblTemporaryAssistanceEntity();
            cjroneblTemporaryAssistanceEntity.setId(welfareMatterApplicationEntity.getMatterId());
            cjroneblTemporaryAssistanceEntity.setStatus(welfareMatterApplicationEntity.getStatus());
            cjroneblTemporaryAssistanceEntity.setSignStatus(welfareMatterApplicationEntity.getSignStatus());
            cjroneblTemporaryAssistanceEntity.setSignatureStatus(welfareMatterApplicationEntity.getSignatureStatus());
            cjroneblTemporaryAssistanceService.updateByIdSign(cjroneblTemporaryAssistanceEntity);
        }else if(signType.indexOf("康复补助") != -1){
            //更新康复补助实体的各种状态
            CjroneblRehabilitationSubsidyEntity cjroneblRehabilitationSubsidyEntity=new CjroneblRehabilitationSubsidyEntity();
            cjroneblRehabilitationSubsidyEntity.setId(welfareMatterApplicationEntity.getMatterId());
            cjroneblRehabilitationSubsidyEntity.setStatus(welfareMatterApplicationEntity.getStatus());
            cjroneblRehabilitationSubsidyEntity.setSignStatus(welfareMatterApplicationEntity.getSignStatus());
            cjroneblRehabilitationSubsidyEntity.setSignatureStatus(welfareMatterApplicationEntity.getSignatureStatus());
            cjroneblRehabilitationSubsidyService.updateByIdSign(cjroneblRehabilitationSubsidyEntity);
        }else if(signType.indexOf("创业补助") != -1){
            //更新创业补助实体的各种状态
            CjroneblBusinessGrantEntity cjroneblBusinessGrantEntity=new CjroneblBusinessGrantEntity();
            cjroneblBusinessGrantEntity.setId(welfareMatterApplicationEntity.getMatterId());
            cjroneblBusinessGrantEntity.setStatus(welfareMatterApplicationEntity.getStatus());
            cjroneblBusinessGrantEntity.setSignStatus(welfareMatterApplicationEntity.getSignStatus());
            cjroneblBusinessGrantEntity.setSignatureStatus(welfareMatterApplicationEntity.getSignatureStatus());
            cjroneblBusinessGrantService.updateByIdSign(cjroneblBusinessGrantEntity);
        }else if(signType.indexOf("大学生补助") != -1){
            //更新创业补助实体的各种状态
            CjroneblCollegeeduEntity cjroneblCollegeeduEntity=new CjroneblCollegeeduEntity();
            cjroneblCollegeeduEntity.setId(welfareMatterApplicationEntity.getMatterId());
            cjroneblCollegeeduEntity.setStatus(welfareMatterApplicationEntity.getStatus());
            cjroneblCollegeeduEntity.setSignStatus(welfareMatterApplicationEntity.getSignStatus());
            cjroneblCollegeeduEntity.setSignatureStatus(welfareMatterApplicationEntity.getSignatureStatus());
            cjroneblCollegeeduService.updateByIdSign(cjroneblCollegeeduEntity);
        } else if(signType.indexOf("残疾人子女教育补贴") != -1){
            //更新实体的各种状态
            CjroneblChildeduEntity cjroneblChildeduEntity=new CjroneblChildeduEntity();
            cjroneblChildeduEntity.setId(welfareMatterApplicationEntity.getMatterId());
            cjroneblChildeduEntity.setStatus(welfareMatterApplicationEntity.getStatus());
            cjroneblChildeduEntity.setSignStatus(welfareMatterApplicationEntity.getSignStatus());
            cjroneblChildeduEntity.setSignatureStatus(welfareMatterApplicationEntity.getSignatureStatus());
            cjroneblChildeduService.updateByIdSign(cjroneblChildeduEntity);
        } else if(signType.indexOf("医疗救助") != -1){
            //更新实体的各种状态
            CjroneblMedicalSupportEntity cjroneblMedicalSupportEntity=new CjroneblMedicalSupportEntity();
            cjroneblMedicalSupportEntity.setId(welfareMatterApplicationEntity.getMatterId());
            cjroneblMedicalSupportEntity.setStatus(welfareMatterApplicationEntity.getStatus());
            cjroneblMedicalSupportEntity.setSignStatus(welfareMatterApplicationEntity.getSignStatus());
            cjroneblMedicalSupportEntity.setSignatureStatus(welfareMatterApplicationEntity.getSignatureStatus());
            cjroneblMedicalSupportService.updateByIdSign(cjroneblMedicalSupportEntity);
        } else if(signType.indexOf("住院补助") != -1){
            //更新实体的各种状态
            CjroneblHospitalizationAllowanceEntity cjroneblHospitalizationAllowanceEntity=new CjroneblHospitalizationAllowanceEntity();
            cjroneblHospitalizationAllowanceEntity.setId(welfareMatterApplicationEntity.getMatterId());
            cjroneblHospitalizationAllowanceEntity.setStatus(welfareMatterApplicationEntity.getStatus());
            cjroneblHospitalizationAllowanceEntity.setSignStatus(welfareMatterApplicationEntity.getSignStatus());
            cjroneblHospitalizationAllowanceEntity.setSignatureStatus(welfareMatterApplicationEntity.getSignatureStatus());
            cjroneblHospitalizationAllowanceService.updateByIdSign(cjroneblHospitalizationAllowanceEntity);
        }else if(signType.indexOf("智慧爱心24小时") != -1){
            //更新实体的各种状态
            Love24Entity love24Entity=new Love24Entity();
            love24Entity.setId(welfareMatterApplicationEntity.getMatterId());
            love24Entity.setStatus(welfareMatterApplicationEntity.getStatus());
            love24Entity.setSignStatus(welfareMatterApplicationEntity.getSignStatus());
            love24Entity.setSignatureStatus(welfareMatterApplicationEntity.getSignatureStatus());
            love24Service.updateByIdSign(love24Entity);
        }

        cjroneWelfareMatterApplicationDao.updateSignStatusById(welfareMatterApplicationEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enable(CjroneWelfareMatterApplicationEntity entity) {
        if ("生活补贴".equals(entity.getMatterName())){
            CjroneLivingAllowanceEntity entity1 = new CjroneLivingAllowanceEntity();
            entity1.setId(entity.getMatterId());
            entity1.setStatus("0");
            cjroneLivingAllowanceService.enable(entity1);
        } else if ("护理补贴".equals(entity.getMatterName())){
            CjroneNursingSubsidyEntity entity1 = new CjroneNursingSubsidyEntity();
            entity1.setId(entity.getMatterId());
            entity1.setStatus("0");
            cjroneNursingSubsidyService.enable(entity1);
        }
        CjroneWelfareMatterApplicationEntity entity2 = new CjroneWelfareMatterApplicationEntity();
        entity2.setStatus("0");
        entity2.setId(entity.getId());
        super.updateById(entity2);
    }

    @Override
    public void saveZljf(CjroneWelfareMatterApplicationEntity cjroneWelfareMatterApplicationEntity) {
        super.save(cjroneWelfareMatterApplicationEntity);
    }

    @Override
    public List<Map<String, Object>> queryFamilyEconoCondition(Map<String, Object> params) {
        return cjroneWelfareMatterApplicationDao.queryFamilyEconoCondition(params);
    }

    @Override
    @Transactional(rollbackFor=Exception.class)
    public boolean save(CjroneWelfareMatterApplicationEntity entity) {
        entity.setCreateTime(null);
        String medical_msg = entity.getMedicalInsurance();
        String status = entity.getStatus();
        entity.setSignStatus("1");   // 1、申请人待手签
        entity.setSignatureStatus("1"); //1、无


        // 根据业务类型决定残疾证验证策略
        Optional<DataDisabilityCertificateEntity> certOpt;
        boolean isChildrenRehabilitation = "1".equals(entity.getIsSnetkfxlApply())||"1".equals(entity.getIsJsbApply());
        
        if (isChildrenRehabilitation) {
            // 残疾少年儿童康复训练补助允许无残疾证
            certOpt = Optional.ofNullable(dataDisabilityCertificateService.getByIDCard(entity.getIdCard()));
        } else {
            // 其他福利项目必须有残疾证
            DataDisabilityCertificateEntity cert = dataDisabilityCertificateService.getByIDCard(entity.getIdCard());
            if (cert == null) {
                throw new RRException("该身份证号未找到残疾证信息，无法申请此项福利");
            }
            certOpt = Optional.of(cert);
        }
        
        //设置残疾类别和残疾等级
        String distype="";
        String disdegree="";
        String[] disinfo=null;
        
        // 安全地获取残疾信息
        if(certOpt.isPresent() && certOpt.get().getDisabilityInfo()!=null){
            String disabilityinfo = certOpt.get().getDisabilityInfo().substring(0,certOpt.get().getDisabilityInfo().length()-1);
            disinfo=disabilityinfo.split(";");
        }
        if(disinfo!=null){
            for(String dis:disinfo){
                distype=distype+dis.substring(0,2)+" ";
            }
        }
        disdegree = certOpt.map(DataDisabilityCertificateEntity::getDisabilityDegree).orElse("");

        // 保存生活补贴
        if("1".equals(entity.getIsShbtApply())){
            CjroneblLivingSubsidyEntity cjroneblLivingSubsidyEntity = gson.fromJson(gson.toJson(entity),CjroneblLivingSubsidyEntity.class);
            cjroneblLivingSubsidyEntity.setDisabilityType(distype);
            cjroneblLivingSubsidyEntity.setDisabilityDegree(disdegree);
            cjroneblLivingSubsidyEntity.setDisableId(certOpt.map(DataDisabilityCertificateEntity::getDisableId).orElse(""));
            cjroneblLivingSubsidyEntity.setCreateTime(DateUtils.format(new Date(), DateUtils.DATE_PATTERN));
            cjroneblLivingSubsidyEntity.setStatus("1");  //申请人待手签
            cjroneblLivingSubsidyEntity.setSignStatus("1");
            cjroneblLivingSubsidyEntity.setSignatureStatus("1");
            cjroneblLivingSubsidyEntity.setSubsidyMoney("224");  //生活补贴金额
            cjroneblLivingSubsidyEntity.setLiveAddress(entity.getPresentAddress());
            cjroneblLivingSubsidyEntity.setTelephone(entity.getMobilePhone());

            cjroneblLivingSubsidyService.save(cjroneblLivingSubsidyEntity);
            entity.setMatterName("生活补贴");
            entity.setMatterId(cjroneblLivingSubsidyEntity.getId());
            entity.setCreateTime(DateUtils.format(new Date(),DateUtils.DATE_TIME_PATTERN));
            super.save(entity);

        }

        // 保存残疾证换领
        if("1".equals(entity.getIsCjzhlApply())){
            CjroneWelfareMatterApplicationEntity entityCJZHL=entity;
            entityCJZHL.setStatus("待审核");
            entityCJZHL.setSignStatus("无");  //该事项不需要手签
            entityCJZHL.setSignatureStatus("无"); //该事项不需要电子盖章

            CjroneblDisabilityCertificateRedemptionEntity cjroneblDisabilityCertificateRedemptionEntity = gson.fromJson(gson.toJson(entityCJZHL),CjroneblDisabilityCertificateRedemptionEntity.class);
            cjroneblDisabilityCertificateRedemptionEntity.setStatus("待审核"); //状态为待审核
            cjroneblDisabilityCertificateRedemptionEntity.setCreateTime(DateUtils.format(new Date(), DateUtils.DATE_PATTERN));
            cjroneblDisabilityCertificateRedemptionEntity.setDisabilityInfo(certOpt.map(DataDisabilityCertificateEntity::getDisabilityInfo).orElse(""));
            cjroneblDisabilityCertificateRedemptionEntity.setCompleteTime(certOpt.map(DataDisabilityCertificateEntity::getCompleteTime).orElse(""));
            cjroneblDisabilityCertificateRedemptionService.save(cjroneblDisabilityCertificateRedemptionEntity);

            entityCJZHL.setMatterName("残疾证换领");
            entityCJZHL.setMatterId(cjroneblDisabilityCertificateRedemptionEntity.getId());
            entityCJZHL.setCreateTime(DateUtils.format(new Date(),DateUtils.DATE_TIME_PATTERN));
            super.save(entityCJZHL);


        }

        // 保存护理补贴
        if ("1".equals(entity.getIsHlbtApply())){

            CjroneblNursingSubsidyEntity cjroneblNursingSubsidyEntity = gson.fromJson(gson.toJson(entity),CjroneblNursingSubsidyEntity.class);
            cjroneblNursingSubsidyEntity.setDisabilityCategory(distype);
            cjroneblNursingSubsidyEntity.setDisabilityDegree(disdegree);
            cjroneblNursingSubsidyEntity.setDisableId(certOpt.map(DataDisabilityCertificateEntity::getDisableId).orElse(""));
            cjroneblNursingSubsidyEntity.setCreateTime(DateUtils.format(new Date(), DateUtils.DATE_PATTERN));
            cjroneblNursingSubsidyEntity.setStatus("1");  //申请人待手签
            cjroneblNursingSubsidyEntity.setSignStatus("1");
            cjroneblNursingSubsidyEntity.setSignatureStatus("1");
            cjroneblNursingSubsidyEntity.setLiveAddress(entity.getPresentAddress());
            //获得补助金额
            Map<String, Object> mapparams =new HashMap<String, Object>();
            //集中托养=3
            mapparams.put("isConcentratedCare",0);
            if ("3".equals(entity.getCareType())){
                mapparams.put("isConcentratedCare",1);
            }
            mapparams.put("type","护理补贴");
            Double subsidymoney=0.0;
            if(disinfo!=null){
                for(String dis:disinfo){
                    mapparams.put("disabilityType",dis.substring(0,2));
                    if("一".equals(dis.substring(2,3))){
                        mapparams.put("disabilityDegree","1");
                    }else if("二".equals(dis.substring(2,3))){
                        mapparams.put("disabilityDegree","2");
                    }else if("三".equals(dis.substring(2,3))){
                        mapparams.put("disabilityDegree","3");
                    }else if("四".equals(dis.substring(2,3))){
                        mapparams.put("disabilityDegree","1");
                    }
                    List<CjroneTwoSubsidyStandardsEntity> cjroneTwoSubsidyStandardsEntityList=cjroneTwoSubsidyStandardsService.queryByMap(mapparams);
                    if(cjroneTwoSubsidyStandardsEntityList!=null&&cjroneTwoSubsidyStandardsEntityList.size()>0) {
                        if(cjroneTwoSubsidyStandardsEntityList.get(0).getMoney()>subsidymoney)
                        subsidymoney = cjroneTwoSubsidyStandardsEntityList.get(0).getMoney();
                    }
                }
            }
            cjroneblNursingSubsidyEntity.setActuallySubsidy(subsidymoney.toString());

            cjroneblNursingSubsidyService.save(cjroneblNursingSubsidyEntity);
            entity.setMatterName("护理补贴");
            entity.setMatterId(cjroneblNursingSubsidyEntity.getId());
            entity.setCreateTime(DateUtils.format(new Date(),DateUtils.DATE_TIME_PATTERN));
            super.save(entity);

        }

        //残疾少年儿童康复训练补助
        if("1".equals(entity.getIsSnetkfxlApply())){
            List<CjroneChildrenRehabilitationSubsidyEntity> childrenRehabilitationSubsidyEntityList = entity.getChildrenRehabilitationSubsidyEntityList();
            for (CjroneChildrenRehabilitationSubsidyEntity subsidy : childrenRehabilitationSubsidyEntityList) {
                entity.setId(null);
                entity.setMatterName("残疾少年儿童康复训练补助");
                entity.setCreateTime(DateUtils.format(new Date(),DateUtils.DATE_TIME_PATTERN));
                super.save(entity);

                BeanUtils.copyProperties(entity,subsidy);

                subsidy.setId(null);
                subsidy.setWelfareMatterApplicationId(entity.getId());
                subsidy.setOperatorId(entity.getCreateId());
                subsidy.setOperatorName(entity.getCreateName());
//                subsidy.setStatus(entity.getStatus());
                cjroneChildrenRehabilitationSubsidyService.save(subsidy);
            }
        }

        //精神病住院补贴
        if("1".equals(entity.getIsJsbApply())){
            List<CjroneMentalIllnessSubsidyEntity> mentalIllnessSubsidyEntitiyList = entity.getMentalIllnessSubsidyEntitiyList();
            for (CjroneMentalIllnessSubsidyEntity subsidy : mentalIllnessSubsidyEntitiyList) {
                entity.setId(null);
                entity.setMatterName("精神病住院补贴");
                entity.setCreateTime(DateUtils.format(new Date(),DateUtils.DATE_TIME_PATTERN));
                super.save(entity);

                BeanUtils.copyProperties(entity,subsidy);

                subsidy.setId(null);

                // 设置nativeZhen字段为当前登录用户的zhen
                SysUserEntity currentUser = ShiroUtils.getUserEntity();
                if (currentUser != null && StringUtils.isNotBlank(currentUser.getZhen())) {
                    entity.setNativeZhen(currentUser.getZhen());
                } else {
                    log.warn("当前登录用户的zhen字段为空或空白字符串，无法设置nativeZhen字段");
                }

                subsidy.setWelfareMatterApplicationId(entity.getId());
                subsidy.setOperatorId(entity.getCreateId());
                subsidy.setOperatorName(entity.getCreateName());
                cjroneMentalIllnessSubsidyService.save(subsidy);
            }
        }

        // 生活补助金
        if("1".equals(entity.getIsShbzjApply())){
            CjroneblLivingAllowanceEntity cjroneblLivingAllowanceEntity = gson.fromJson(gson.toJson(entity),CjroneblLivingAllowanceEntity.class);
            cjroneblLivingAllowanceEntity.setDisabilityType(distype);
            cjroneblLivingAllowanceEntity.setDisabilityDegree(disdegree);
            cjroneblLivingAllowanceEntity.setDisableId(certOpt.map(DataDisabilityCertificateEntity::getDisableId).orElse(""));
            cjroneblLivingAllowanceEntity.setCreateTime(DateUtils.format(new Date(), DateUtils.DATE_PATTERN));
            cjroneblLivingAllowanceEntity.setStatus("1");  //申请人待手签
            cjroneblLivingAllowanceEntity.setSignStatus("1");
            cjroneblLivingAllowanceEntity.setSignatureStatus("1");
            cjroneblLivingAllowanceEntity.setLiveAddress(entity.getPresentAddress());
            cjroneblLivingAllowanceEntity.setTelephone(entity.getMobilePhone());
            cjroneblLivingAllowanceEntity.setFamilyEconomy(entity.getFamilyEconoCondition());

            // 计算补助金额
            // 1、判断年龄
            int age = getAgeByBirthDay(cjroneblLivingAllowanceEntity.getBirthday());
            cjroneblLivingAllowanceEntity.setAge(age);
            if(age<16){
                //2、判断残疾等级
                if("一级".equals(disdegree)||"二级".equals(disdegree)){
                    cjroneblLivingAllowanceEntity.setSubsidyMoney("150");
                } else if("三级".equals(disdegree)||"四级".equals(disdegree)){
                    cjroneblLivingAllowanceEntity.setSubsidyMoney("100");
                }
                cjroneblLivingAllowanceEntity.setApplyType("残疾儿童生活补助金");
            }else{
                if("1".equals(cjroneblLivingAllowanceEntity.getSex())){
                    //男
                    if(age>60){
                        if("一级".equals(disdegree)||"二级".equals(disdegree)){
                            cjroneblLivingAllowanceEntity.setSubsidyMoney("100");
                        } else if("三级".equals(disdegree)||"四级".equals(disdegree)){
                            cjroneblLivingAllowanceEntity.setSubsidyMoney("70");
                        }
                        cjroneblLivingAllowanceEntity.setApplyType("老年残疾人生活补助金");
                    }
                }else{
                    //女
                    if(age>55){
                        if("一级".equals(disdegree)||"二级".equals(disdegree)){
                            cjroneblLivingAllowanceEntity.setSubsidyMoney("100");
                        } else if("三级".equals(disdegree)||"四级".equals(disdegree)){
                            cjroneblLivingAllowanceEntity.setSubsidyMoney("70");
                        }
                        cjroneblLivingAllowanceEntity.setApplyType("老年残疾人生活补助金");
                    }
                }
            }

            cjroneblLivingAllowanceService.save(cjroneblLivingAllowanceEntity);
            entity.setMatterName("生活补助金");
            entity.setMatterId(cjroneblLivingAllowanceEntity.getId());
            entity.setCreateTime(DateUtils.format(new Date(),DateUtils.DATE_TIME_PATTERN));
            super.save(entity);

            // 保存上传的附件
            List<CjroneWelfareApplyDocEntity> documentList = new Gson().fromJson(entity.getShbzjDoc(), new TypeToken<List<CjroneWelfareApplyDocEntity>>() {
            }.getType());
            documentList.forEach(item ->{
                System.out.println(new Gson().toJson(item));
                item.setWelfareMatterApplicationId(cjroneblLivingAllowanceEntity.getId());
                item.setDocumentId(item.getDocumentId());
                cjroneWelfareApplyDocService.save(item);
            });

        }

        // 职工基本养老保险补助
        if("1".equals(entity.getIsZgjbylApply())){
            CjroneblZgjbyanglaoEntity cjroneblZgjbyanglaoEntity = gson.fromJson(gson.toJson(entity),CjroneblZgjbyanglaoEntity.class);
            int age = getAgeByBirthDay(entity.getBirthday());
            cjroneblZgjbyanglaoEntity.setAge(age);
            cjroneblZgjbyanglaoEntity.setSubsidyMoney((Double.valueOf(cjroneblZgjbyanglaoEntity.getPayMoney())/2)+"");
            cjroneblZgjbyanglaoEntity.setCreateTime(DateUtils.format(new Date(), DateUtils.DATE_PATTERN));
            cjroneblZgjbyanglaoEntity.setStatus("1");  //申请人待手签
            cjroneblZgjbyanglaoEntity.setSignStatus("1");
            cjroneblZgjbyanglaoEntity.setSignatureStatus("1");
            cjroneblZgjbyanglaoEntity.setLiveAddress(entity.getPresentAddress());
            cjroneblZgjbyanglaoEntity.setTelephone(entity.getMobilePhone());
            cjroneblZgjbyanglaoEntity.setInsuredStatus("个体工商户");
            if("1".equals(cjroneblZgjbyanglaoEntity.getOtherSubsidy())){
                cjroneblZgjbyanglaoEntity.setOtherSubsidy("40、50灵活就业社保补助");
            }else if("2".equals(cjroneblZgjbyanglaoEntity.getOtherSubsidy())){
                cjroneblZgjbyanglaoEntity.setOtherSubsidy("用工补助");
            }else if("3".equals(cjroneblZgjbyanglaoEntity.getOtherSubsidy())){
                cjroneblZgjbyanglaoEntity.setOtherSubsidy("其他");
            }

            cjroneblZgjbyanglaoService.save(cjroneblZgjbyanglaoEntity);
            entity.setMatterName("职工基本养老保险补助");
            entity.setMatterId(cjroneblZgjbyanglaoEntity.getId());
            entity.setCreateTime(DateUtils.format(new Date(),DateUtils.DATE_TIME_PATTERN));
            super.save(entity);
        }

        //城乡居民养老保险
        if("1".equals(entity.getIsCxjmylApply())){
            CjroneblCxjmyanglaoEntity cjroneblCxjmyanglaoEntity = gson.fromJson(gson.toJson(entity),CjroneblCxjmyanglaoEntity.class);
            int age = getAgeByBirthDay(entity.getBirthday());
            cjroneblCxjmyanglaoEntity.setAge(age);

            cjroneblCxjmyanglaoEntity.setCreateTime(DateUtils.format(new Date(), DateUtils.DATE_PATTERN));
            cjroneblCxjmyanglaoEntity.setStatus("1");  //申请人待手签
            cjroneblCxjmyanglaoEntity.setSignStatus("1");
            cjroneblCxjmyanglaoEntity.setSignatureStatus("1");
            cjroneblCxjmyanglaoEntity.setLiveAddress(entity.getPresentAddress());
            cjroneblCxjmyanglaoEntity.setTelephone(entity.getMobilePhone());
            cjroneblCxjmyanglaoEntity.setInsuredStatus("自由职业者");
            cjroneblCxjmyanglaoEntity.setOtherSubsidy(entity.getOtherSubsidy3());
            cjroneblCxjmyanglaoEntity.setSeTime(entity.getSeTime3());
            cjroneblCxjmyanglaoEntity.setPayMoney(entity.getPayMoney3());
            if("1".equals(cjroneblCxjmyanglaoEntity.getOtherSubsidy())){
                cjroneblCxjmyanglaoEntity.setOtherSubsidy("40、50灵活就业社保补助");
            }else if("2".equals(cjroneblCxjmyanglaoEntity.getOtherSubsidy())){
                cjroneblCxjmyanglaoEntity.setOtherSubsidy("用工补助");
            }else if("3".equals(cjroneblCxjmyanglaoEntity.getOtherSubsidy())){
                cjroneblCxjmyanglaoEntity.setOtherSubsidy("其他");
            }

            cjroneblCxjmyanglaoEntity.setSubsidyMoney((Double.parseDouble(cjroneblCxjmyanglaoEntity.getPayMoney())/2)+"");

            cjroneblCxjmyanglaoService.save(cjroneblCxjmyanglaoEntity);
            entity.setMatterName("城乡居民养老保险补助");
            entity.setMatterId(cjroneblCxjmyanglaoEntity.getId());
            entity.setCreateTime(DateUtils.format(new Date(),DateUtils.DATE_TIME_PATTERN));
            super.save(entity);
        }

        // 职工基本医疗保险补助
        if("1".equals(entity.getIsZgjbylbxApply())){
            CjroneblZgjbyiliaoEntity cjroneblZgjbyiliaoEntity = gson.fromJson(gson.toJson(entity),CjroneblZgjbyiliaoEntity.class);
            int age = getAgeByBirthDay(entity.getBirthday());
            cjroneblZgjbyiliaoEntity.setAge(age);

            cjroneblZgjbyiliaoEntity.setCreateTime(DateUtils.format(new Date(), DateUtils.DATE_PATTERN));
            cjroneblZgjbyiliaoEntity.setStatus("1");  //申请人待手签
            cjroneblZgjbyiliaoEntity.setSignStatus("1");
            cjroneblZgjbyiliaoEntity.setSignatureStatus("1");
            cjroneblZgjbyiliaoEntity.setLiveAddress(entity.getPresentAddress());
            cjroneblZgjbyiliaoEntity.setTelephone(entity.getMobilePhone());
            cjroneblZgjbyiliaoEntity.setInsuredStatus("个体工商户");
            cjroneblZgjbyiliaoEntity.setOtherSubsidy(entity.getOtherSubsidy2());
            cjroneblZgjbyiliaoEntity.setSeTime(entity.getSeTime2());
            cjroneblZgjbyiliaoEntity.setPayMoney(entity.getPayMoney2());
            if("1".equals(cjroneblZgjbyiliaoEntity.getOtherSubsidy())){
                cjroneblZgjbyiliaoEntity.setOtherSubsidy("40、50灵活就业社保补助");
            }else if("2".equals(cjroneblZgjbyiliaoEntity.getOtherSubsidy())){
                cjroneblZgjbyiliaoEntity.setOtherSubsidy("用工补助");
            }else if("3".equals(cjroneblZgjbyiliaoEntity.getOtherSubsidy())){
                cjroneblZgjbyiliaoEntity.setOtherSubsidy("其他");
            }
            cjroneblZgjbyiliaoEntity.setSubsidyMoney((Double.parseDouble(cjroneblZgjbyiliaoEntity.getPayMoney())/2)+"");
            cjroneblZgjbyiliaoService.save(cjroneblZgjbyiliaoEntity);
            entity.setMatterName("职工基本医疗保险补助");
            entity.setMatterId(cjroneblZgjbyiliaoEntity.getId());
            entity.setCreateTime(DateUtils.format(new Date(),DateUtils.DATE_TIME_PATTERN));
            super.save(entity);
        }

        // 城乡基本医疗保险补助
        if("1".equals(entity.getIsCxjbylbxApply())){
            CjroneblCxjmyiliaoEntity cjroneblCxjmyiliaoEntity = gson.fromJson(gson.toJson(entity),CjroneblCxjmyiliaoEntity.class);
            int age = getAgeByBirthDay(entity.getBirthday());
            cjroneblCxjmyiliaoEntity.setAge(age);

            cjroneblCxjmyiliaoEntity.setCreateTime(DateUtils.format(new Date(), DateUtils.DATE_PATTERN));
            cjroneblCxjmyiliaoEntity.setStatus("1");  //申请人待手签
            cjroneblCxjmyiliaoEntity.setSignStatus("1");
            cjroneblCxjmyiliaoEntity.setSignatureStatus("1");
            cjroneblCxjmyiliaoEntity.setLiveAddress(entity.getPresentAddress());
            cjroneblCxjmyiliaoEntity.setTelephone(entity.getMobilePhone());
            cjroneblCxjmyiliaoEntity.setInsuredStatus("自由职业者");
            cjroneblCxjmyiliaoEntity.setOtherSubsidy(entity.getOtherSubsidy4());
            cjroneblCxjmyiliaoEntity.setSeTime(entity.getSeTime4());
            cjroneblCxjmyiliaoEntity.setPayMoney(entity.getPayMoney4());
            if("1".equals(cjroneblCxjmyiliaoEntity.getOtherSubsidy())){
                cjroneblCxjmyiliaoEntity.setOtherSubsidy("40、50灵活就业社保补助");
            }else if("2".equals(cjroneblCxjmyiliaoEntity.getOtherSubsidy())){
                cjroneblCxjmyiliaoEntity.setOtherSubsidy("用工补助");
            }else if("3".equals(cjroneblCxjmyiliaoEntity.getOtherSubsidy())){
                cjroneblCxjmyiliaoEntity.setOtherSubsidy("其他");
            }
            cjroneblCxjmyiliaoEntity.setSubsidyMoney((Double.parseDouble(cjroneblCxjmyiliaoEntity.getPayMoney())/2)+"");
            cjroneblCxjmyiliaoService.save(cjroneblCxjmyiliaoEntity);
            entity.setMatterName("城乡基本医疗保险补助");
            entity.setMatterId(cjroneblCxjmyiliaoEntity.getId());
            entity.setCreateTime(DateUtils.format(new Date(),DateUtils.DATE_TIME_PATTERN));
            super.save(entity);
        }

        //残疾人临时救助
        if("1".equals(entity.getIsLsjzApply())){
            CjroneblTemporaryAssistanceEntity cjroneblTemporaryAssistanceEntity = gson.fromJson(gson.toJson(entity),CjroneblTemporaryAssistanceEntity.class);
            int age = getAgeByBirthDay(entity.getBirthday());

            cjroneblTemporaryAssistanceEntity.setCreateTime(DateUtils.format(new Date(), DateUtils.DATE_PATTERN));
            cjroneblTemporaryAssistanceEntity.setStatus("1");  //申请人待手签
            cjroneblTemporaryAssistanceEntity.setSignStatus("1");
            cjroneblTemporaryAssistanceEntity.setSignatureStatus("1");
            cjroneblTemporaryAssistanceEntity.setLiveAddress(entity.getPresentAddress());
            cjroneblTemporaryAssistanceEntity.setTelephone(entity.getMobilePhone());
            cjroneblTemporaryAssistanceEntity.setFamilyEconomy(entity.getFamilyEconoCondition());
            cjroneblTemporaryAssistanceEntity.setPayMoney(entity.getLsjzpayMoney());
            cjroneblTemporaryAssistanceEntity.setSubsidyMoney(entity.getLsjzsubsidyMoney());
            cjroneblTemporaryAssistanceEntity.setApplyReason(entity.getLsjzapplyReason());
            cjroneblTemporaryAssistanceEntity.setDisabilityType(distype);
            cjroneblTemporaryAssistanceEntity.setDisabilityDegree(disdegree);

            cjroneblTemporaryAssistanceService.save(cjroneblTemporaryAssistanceEntity);
            entity.setMatterName("残疾人临时救助");
            entity.setMatterId(cjroneblTemporaryAssistanceEntity.getId());
            entity.setCreateTime(DateUtils.format(new Date(),DateUtils.DATE_TIME_PATTERN));
            super.save(entity);
        }

        // 康复补助
        if("1".equals(entity.getIsKfbzApply())){
            CjroneblRehabilitationSubsidyEntity cjroneblRehabilitationSubsidyEntity = gson.fromJson(gson.toJson(entity),CjroneblRehabilitationSubsidyEntity.class);
            int age = getAgeByBirthDay(entity.getBirthday());

            cjroneblRehabilitationSubsidyEntity.setCreateTime(DateUtils.format(new Date(), DateUtils.DATE_PATTERN));
            cjroneblRehabilitationSubsidyEntity.setStatus("1");  //申请人待手签
            cjroneblRehabilitationSubsidyEntity.setSignStatus("1");
            cjroneblRehabilitationSubsidyEntity.setSignatureStatus("1");
            cjroneblRehabilitationSubsidyEntity.setLiveAddress(entity.getPresentAddress());
            cjroneblRehabilitationSubsidyEntity.setTelephone(entity.getMobilePhone());
            cjroneblRehabilitationSubsidyEntity.setFamilyEconomy(entity.getFamilyEconoCondition());
            cjroneblRehabilitationSubsidyEntity.setDisabilityType(distype);
            cjroneblRehabilitationSubsidyEntity.setDisabilityDegree(disdegree);
            cjroneblRehabilitationSubsidyEntity.setYlbx(entity.getMedicalInsurance());
            cjroneblRehabilitationSubsidyEntity.setDisabileId(entity.getDisableId());
            cjroneblRehabilitationSubsidyEntity.setNationailty(entity.getNationality());
            cjroneblRehabilitationSubsidyEntity.setGuardianTelephone(entity.getGuardianPhone());

            cjroneblRehabilitationSubsidyService.save(cjroneblRehabilitationSubsidyEntity);
            entity.setMatterName("康复补助");
            entity.setMatterId(cjroneblRehabilitationSubsidyEntity.getId());
            entity.setCreateTime(DateUtils.format(new Date(),DateUtils.DATE_TIME_PATTERN));
            super.save(entity);

            // 保存上传的附件
            List<CjroneWelfareApplyDocEntity> documentList = new Gson().fromJson(entity.getKfbzDoc(), new TypeToken<List<CjroneWelfareApplyDocEntity>>() {
            }.getType());
            documentList.forEach(item ->{
                System.out.println(new Gson().toJson(item));
                item.setWelfareMatterApplicationId(cjroneblRehabilitationSubsidyEntity.getId());
                item.setDocumentId(item.getDocumentId());
                cjroneWelfareApplyDocService.save(item);
            });
        }

        //创业补助
        if("1".equals(entity.getIsCybzApply())){
            CjroneblBusinessGrantEntity cjroneblBusinessGrantEntity = gson.fromJson(gson.toJson(entity),CjroneblBusinessGrantEntity.class);
            int age = getAgeByBirthDay(entity.getBirthday());

            cjroneblBusinessGrantEntity.setCreateTime(DateUtils.format(new Date(), DateUtils.DATE_PATTERN));
            cjroneblBusinessGrantEntity.setStatus("1");  //申请人待手签
            cjroneblBusinessGrantEntity.setSignStatus("1");
            cjroneblBusinessGrantEntity.setSignatureStatus("1");
            cjroneblBusinessGrantEntity.setLiveAddress(entity.getPresentAddress());
            cjroneblBusinessGrantEntity.setTelephone(entity.getMobilePhone());
            cjroneblBusinessGrantEntity.setFamilyEconomy(entity.getFamilyEconoCondition());
            cjroneblBusinessGrantEntity.setSubsidyMoney(entity.getCysubsidyMoney());
            cjroneblBusinessGrantEntity.setAge(age);

            cjroneblBusinessGrantService.save(cjroneblBusinessGrantEntity);
            entity.setMatterName("创业补助");
            entity.setMatterId(cjroneblBusinessGrantEntity.getId());
            entity.setCreateTime(DateUtils.format(new Date(),DateUtils.DATE_TIME_PATTERN));
            super.save(entity);
        }

        // 大学生补助
        if("1".equals(entity.getIsCollegeeduApply())){
            CjroneblCollegeeduEntity cjroneblCollegeeduEntity = gson.fromJson(gson.toJson(entity),CjroneblCollegeeduEntity.class);

            cjroneblCollegeeduEntity.setCreateTime(DateUtils.format(new Date(), DateUtils.DATE_PATTERN));
            cjroneblCollegeeduEntity.setStatus("1");  //申请人待手签
            cjroneblCollegeeduEntity.setSignStatus("1");
            cjroneblCollegeeduEntity.setSignatureStatus("1");
            cjroneblCollegeeduEntity.setLiveAddress(entity.getPresentAddress());
            cjroneblCollegeeduEntity.setTelephone(entity.getMobilePhone());
            cjroneblCollegeeduEntity.setSubsidyReason(entity.getCollegesubsidyReason());

            cjroneblCollegeeduService.save(cjroneblCollegeeduEntity);
            entity.setMatterName("大学生补助");
            entity.setMatterId(cjroneblCollegeeduEntity.getId());
            entity.setCreateTime(DateUtils.format(new Date(),DateUtils.DATE_TIME_PATTERN));
            super.save(entity);
        }

        // 学历教育补助 残疾人子女教育补贴
        if("1".equals(entity.getIsChildeduApply())){
            CjroneblChildeduEntity cjroneblChildeduEntity = gson.fromJson(gson.toJson(entity),CjroneblChildeduEntity.class);

            cjroneblChildeduEntity.setCreateTime(DateUtils.format(new Date(), DateUtils.DATE_PATTERN));
            cjroneblChildeduEntity.setStatus("1");  //申请人待手签
            cjroneblChildeduEntity.setSignStatus("1");
            cjroneblChildeduEntity.setSignatureStatus("1");
            cjroneblChildeduEntity.setLiveAddress(entity.getPresentAddress());
            cjroneblChildeduEntity.setTelephoe(entity.getMobilePhone());
//            cjroneblChildeduEntity.set(entity.getCollegesubsidyReason());

            cjroneblChildeduService.save(cjroneblChildeduEntity);
            entity.setMatterName("残疾人子女教育补贴");
            entity.setMatterId(cjroneblChildeduEntity.getId());
            entity.setCreateTime(DateUtils.format(new Date(),DateUtils.DATE_TIME_PATTERN));
            super.save(entity);
        }

        // 医疗救助
        if("1".equals(entity.getIsYljzApply())){
            CjroneblMedicalSupportEntity cjroneblMedicalSupportEntity = gson.fromJson(gson.toJson(entity),CjroneblMedicalSupportEntity.class);

            cjroneblMedicalSupportEntity.setCreateTime(DateUtils.format(new Date(), DateUtils.DATE_PATTERN));
            cjroneblMedicalSupportEntity.setStatus("1");  //申请人待手签
            cjroneblMedicalSupportEntity.setSignStatus("1");
            cjroneblMedicalSupportEntity.setSignatureStatus("1");
            cjroneblMedicalSupportEntity.setLiveAddress(entity.getPresentAddress());
            cjroneblMedicalSupportEntity.setTelephone(entity.getMobilePhone());
            cjroneblMedicalSupportEntity.setZyTime(entity.getYljzzyTime());

            cjroneblMedicalSupportService.save(cjroneblMedicalSupportEntity);
            entity.setMatterName("医疗救助");
            entity.setMatterId(cjroneblMedicalSupportEntity.getId());
            entity.setCreateTime(DateUtils.format(new Date(),DateUtils.DATE_TIME_PATTERN));
            super.save(entity);
        }

        // 住院补助
        if("1".equals(entity.getIsZybzApply())){
            CjroneblHospitalizationAllowanceEntity cjroneblHospitalizationAllowanceEntity = gson.fromJson(gson.toJson(entity),CjroneblHospitalizationAllowanceEntity.class);

            cjroneblHospitalizationAllowanceEntity.setCreateTime(DateUtils.format(new Date(), DateUtils.DATE_PATTERN));
            cjroneblHospitalizationAllowanceEntity.setStatus("1");  //申请人待手签
            cjroneblHospitalizationAllowanceEntity.setSignStatus("1");
            cjroneblHospitalizationAllowanceEntity.setSignatureStatus("1");
            cjroneblHospitalizationAllowanceEntity.setLiveAddress(entity.getPresentAddress());
            cjroneblHospitalizationAllowanceEntity.setTelephone(entity.getMobilePhone());
            cjroneblHospitalizationAllowanceEntity.setZyTime(entity.getYljzzyTime());

            cjroneblHospitalizationAllowanceService.save(cjroneblHospitalizationAllowanceEntity);
            entity.setMatterName("住院补助");
            entity.setMatterId(cjroneblHospitalizationAllowanceEntity.getId());
            entity.setCreateTime(DateUtils.format(new Date(),DateUtils.DATE_TIME_PATTERN));
            super.save(entity);
        }

        // 智慧 爱心24小时
        if("1".equals(entity.getIsLove24Apply())){
            Love24Entity love24Entity = gson.fromJson(gson.toJson(entity),Love24Entity.class);
            love24Entity.setCreateTime(DateUtils.format(new Date(), DateUtils.DATE_PATTERN));
            //无需手签和盖章，保存后都为完成状态 2021-03-17
            love24Entity.setStatus("8");
            love24Entity.setStatusOptions("通过");
            love24Entity.setSignStatus("8");
            love24Entity.setSignatureStatus("6");
            love24Entity.setDisableType(distype);
            love24Entity.setGuardian(entity.getGuardianName());
            love24Entity.setLiveAddress(entity.getPresentAddress());
            love24Entity.setTelephone(entity.getGuardianPhone());

            love24Service.save(love24Entity);
            entity.setMatterName("智慧爱心24小时");
            entity.setMatterId(love24Entity.getId());
            entity.setCreateTime(DateUtils.format(new Date(),DateUtils.DATE_TIME_PATTERN));
            super.save(entity);
        }

        return true;

    }

    public static int getAgeByBirthDay(String birthDay){
        if (birthDay == null || birthDay.length()<4) {
            return 0;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //得到当前的年份
        String cYear = sdf.format(new Date()).substring(0,4);
        String cMouth = sdf.format(new Date()).substring(5,7);
        String cDay = sdf.format(new Date()).substring(8,10);
        //得到生日年份
        String birth_Year = birthDay .substring(0,4);
        String birth_Mouth = birthDay .substring(5,7);
        String birth_Day = birthDay .substring(8,10);
        //年月日比较后得到年龄
        int age = Integer.parseInt(cYear) - Integer.parseInt(birth_Year);
        if ((Integer.parseInt(cMouth) - Integer.parseInt(birth_Mouth))<0) {
            age=age-1;
        }else if ((Integer.parseInt(cMouth) - Integer.parseInt(birth_Mouth))==0) {
            if ( (Integer.parseInt(cDay) - Integer.parseInt(birth_Day))>0) {
                age=age-1;
            }else {
                age = Integer.parseInt(cYear) - Integer.parseInt(birth_Year);
            }
        }else if ((Integer.parseInt(cMouth) - Integer.parseInt(birth_Mouth))>0) {
            age = Integer.parseInt(cYear) - Integer.parseInt(birth_Year);
        }
        return age;
    }

}
