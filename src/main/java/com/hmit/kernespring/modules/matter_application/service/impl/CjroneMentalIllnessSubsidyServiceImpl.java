package com.hmit.kernespring.modules.matter_application.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.cjrone.constants.CjroneConstants;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.stream.Collectors;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.matter_application.dao.CjroneMentalIllnessSubsidyDao;
import com.hmit.kernespring.modules.matter_application.entity.CjroneMentalIllnessSubsidyEntity;
import com.hmit.kernespring.modules.matter_application.entity.CjroneWelfareMatterApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.CjroneMentalIllnessSubsidyService;
import com.hmit.kernespring.modules.matter_application.service.CjroneWelfareMatterApplicationService;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;
import java.util.Date;
import org.springframework.transaction.annotation.Transactional;


@Service("cjroneMentalIllnessSubsidyService")
public class CjroneMentalIllnessSubsidyServiceImpl extends ServiceImpl<CjroneMentalIllnessSubsidyDao, CjroneMentalIllnessSubsidyEntity> implements CjroneMentalIllnessSubsidyService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneMentalIllnessSubsidyDao cjroneMentalIllnessSubsidyDao;
    @Autowired
    private SysDictService sysDictService;
    @Autowired
    private CjroneWelfareMatterApplicationService cjroneWelfareMatterApplicationService;
    @Autowired
    private CjroneWelfareMatterApplicationDao cjroneWelfareMatterApplicationDao;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneMentalIllnessSubsidyEntity entity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneMentalIllnessSubsidyEntity.class);
        IPage<CjroneMentalIllnessSubsidyEntity> page = this.page(
                new Query<CjroneMentalIllnessSubsidyEntity>().getPage(params),
                getWrapper(entity)
        );
//        Map<String, Object> params_map = new HashMap<>();
//        params_map.put("redis_key","sys_dict:all");
//        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
//        page.getRecords().forEach( item -> {
//            SysDictEntity status_sysDictEntity = sys_dict_all_list.stream().filter(
//                iii->iii.getCode().equals("zt_0000") && iii.getValue().equals(
//                        item.getStatus ())).findAny().orElse(null);
//            if (status_sysDictEntity != null){
//                item.setStatus (status_sysDictEntity.getLabel());
//            }else{
//                item.setStatus (null);
//            }
//        });
        return new PageUtils(page);
    }
    @Override
    public List<CjroneMentalIllnessSubsidyEntity> queryExportData(Map<String, Object> params) {
        // 直接从URL参数构建实体对象，不依赖JSON解析
        CjroneMentalIllnessSubsidyEntity entity = new CjroneMentalIllnessSubsidyEntity();
        
        // 从URL参数中设置查询条件
        if (params.get("name") != null) entity.setName(params.get("name").toString());
        if (params.get("sex") != null) entity.setSex(params.get("sex").toString());
        if (params.get("idCard") != null) entity.setIdCard(params.get("idCard").toString());
        if (params.get("mobilePhone") != null) entity.setMobilePhone(params.get("mobilePhone").toString());
        if (params.get("nationality") != null) entity.setNationality(params.get("nationality").toString());
        if (params.get("status") != null) entity.setStatus(params.get("status").toString());
        if (params.get("subsidyYear") != null) entity.setSubsidyYear(Integer.valueOf(params.get("subsidyYear").toString()));
        if (params.get("hospitalName") != null) entity.setHospitalName(params.get("hospitalName").toString());
        if (params.get("isPsychiatric") != null) entity.setIsPsychiatric(Boolean.valueOf(params.get("isPsychiatric").toString()));
        if (params.get("operatorName") != null) entity.setOperatorName(params.get("operatorName").toString());
        if (params.get("auditorName") != null) entity.setAuditorName(params.get("auditorName").toString());
        if (params.get("serialNumber") != null) entity.setSerialNumber(params.get("serialNumber").toString());
        if (params.get("hospitalizationCount") != null) entity.setHospitalizationCount(Integer.valueOf(params.get("hospitalizationCount").toString()));
        
        return this.list(getWrapper(entity));
    }

    @Override
    public List<CjroneMentalIllnessSubsidyEntity> queryHistoryByIdCard(String idCard, Integer status, String subsidyYear) {
        QueryWrapper<CjroneMentalIllnessSubsidyEntity>queryWrapper=new QueryWrapper<CjroneMentalIllnessSubsidyEntity>()
                .eq(StringUtils.isNotBlank(idCard), "id_card", idCard)
                .eq(status!=null,"status", status)
                .eq(StringUtils.isNotBlank(subsidyYear), "subsidy_year", subsidyYear)
                .orderByDesc("created_at")
                ;

        return this.list(queryWrapper);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAudioById(CjroneMentalIllnessSubsidyEntity cjroneMentalIllnessSubsidy) {
        // 检查年度累计金额是否超过限制
        if (cjroneMentalIllnessSubsidy.getSubsidyYear() != null &&
            cjroneMentalIllnessSubsidy.getSubsidyAmount() != null &&
            "8".equals(cjroneMentalIllnessSubsidy.getStatus())) {
            boolean overLimit = checkYearTotalAmountLimit(
                cjroneMentalIllnessSubsidy.getIdCard(),
                cjroneMentalIllnessSubsidy.getSubsidyYear(),
                cjroneMentalIllnessSubsidy.getSubsidyAmount()
            );
            
            if (overLimit) {
                return false;
            }
        }
        
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id", cjroneMentalIllnessSubsidy.getId());
        map.put("matter_name", "精神病住院补贴");
        map.put("verify_time", new Date());
        map.put("status", cjroneMentalIllnessSubsidy.getStatus());
        if (cjroneMentalIllnessSubsidy.getStatus() != null) {
            cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        }
        cjroneMentalIllnessSubsidy.setApprovalTime(new Date());
        super.updateById(cjroneMentalIllnessSubsidy);
        return true;
    }

    @Override
    public Map<String, Object> statistics(String approvalYear) {
       return cjroneMentalIllnessSubsidyDao.queryStatistics(approvalYear);
    }
    
    @Override
    public BigDecimal calculateYearTotalAmount(String idCard, Integer subsidyYear) {
        // 使用DAO层的方法直接计算年度累计金额
        return cjroneMentalIllnessSubsidyDao.calculateYearTotalAmount(idCard, subsidyYear);
    }
    
    @Override
    public boolean checkYearTotalAmountLimit(String idCard, Integer subsidyYear, BigDecimal newAmount) {
        // 如果新金额为null，则认为没有限制
        if (newAmount == null) {
            return false;
        }
        
        // 计算当前年度已累计的金额
        BigDecimal currentYearTotal = calculateYearTotalAmount(idCard, subsidyYear);
        
        // 年度累计金额限制
        BigDecimal limitAmount = CjroneConstants.MENTAL_ILLNESS_SUBSIDY_YEAR_TOTAL_AMOUNT_LIMIT;
        
        // 检查加上新金额后是否超过限制
        BigDecimal totalWithNewAmount = currentYearTotal.add(newAmount);
        return totalWithNewAmount.compareTo(limitAmount) > 0;
    }

    /**
     * 构建查询条件Wrapper
     * @param entity 查询条件实体
     * @return QueryWrapper
     */
    private QueryWrapper<CjroneMentalIllnessSubsidyEntity> getWrapper(CjroneMentalIllnessSubsidyEntity entity) {
        return new QueryWrapper<CjroneMentalIllnessSubsidyEntity>()
            .eq(StringUtils.isNotBlank(entity.getId ()!=null && !"".equals(entity.getId ().toString())? entity.getId ().toString():null),"id", entity.getId ())
            .eq(StringUtils.isNotBlank(entity.getAnnualAccumulatedAmount ()!=null && !"".equals(entity.getAnnualAccumulatedAmount ().toString())? entity.getAnnualAccumulatedAmount ().toString():null),"annual_accumulated_amount", entity.getAnnualAccumulatedAmount ())
            .eq(StringUtils.isNotBlank(entity.getIsPsychiatric ()!=null && !"".equals(entity.getIsPsychiatric ().toString())? entity.getIsPsychiatric ().toString():null),"is_psychiatric", entity.getIsPsychiatric ())
            .eq(StringUtils.isNotBlank(entity.getHospitalName ()!=null && !"".equals(entity.getHospitalName ().toString())? entity.getHospitalName ().toString():null),"hospital_name", entity.getHospitalName ())
            .eq(StringUtils.isNotBlank(entity.getSerialNumber ()!=null && !"".equals(entity.getSerialNumber ().toString())? entity.getSerialNumber ().toString():null),"serial_number", entity.getSerialNumber ())
            .eq(StringUtils.isNotBlank(entity.getApplicationDate ()!=null && !"".equals(entity.getApplicationDate ().toString())? entity.getApplicationDate ().toString():null),"application_date", entity.getApplicationDate ())
            .eq(StringUtils.isNotBlank(entity.getHospitalizationStartDate ()!=null && !"".equals(entity.getHospitalizationStartDate ().toString())? entity.getHospitalizationStartDate ().toString():null),"hospitalization_start_date", entity.getHospitalizationStartDate ())
            .eq(StringUtils.isNotBlank(entity.getHospitalizationEndDate ()!=null && !"".equals(entity.getHospitalizationEndDate ().toString())? entity.getHospitalizationEndDate ().toString():null),"hospitalization_end_date", entity.getHospitalizationEndDate ())
            .eq(StringUtils.isNotBlank(entity.getSubsidyYear ()!=null && !"".equals(entity.getSubsidyYear ().toString())? entity.getSubsidyYear ().toString():null),"subsidy_year", entity.getSubsidyYear ())
            .eq(StringUtils.isNotBlank(entity.getHospitalizationCount ()!=null && !"".equals(entity.getHospitalizationCount ().toString())? entity.getHospitalizationCount ().toString():null),"hospitalization_count", entity.getHospitalizationCount ())
            .eq(StringUtils.isNotBlank(entity.getPersonalPayment ()!=null && !"".equals(entity.getPersonalPayment ().toString())? entity.getPersonalPayment ().toString():null),"personal_payment", entity.getPersonalPayment ())
            .eq(StringUtils.isNotBlank(entity.getClassBPayment ()!=null && !"".equals(entity.getClassBPayment ().toString())? entity.getClassBPayment ().toString():null),"class_b_payment", entity.getClassBPayment ())
            .eq(StringUtils.isNotBlank(entity.getInsuranceOverageLimit ()!=null && !"".equals(entity.getInsuranceOverageLimit ().toString())? entity.getInsuranceOverageLimit ().toString():null),"insurance_overage_limit", entity.getInsuranceOverageLimit ())
            .eq(StringUtils.isNotBlank(entity.getEffectiveOopMedicalExpense ()!=null && !"".equals(entity.getEffectiveOopMedicalExpense ().toString())? entity.getEffectiveOopMedicalExpense ().toString():null),"effective_oop_medical_expense", entity.getEffectiveOopMedicalExpense ())
            .eq(StringUtils.isNotBlank(entity.getSubsidyAmount ()!=null && !"".equals(entity.getSubsidyAmount ().toString())? entity.getSubsidyAmount ().toString():null),"subsidy_amount", entity.getSubsidyAmount ())
            .eq(StringUtils.isNotBlank(entity.getApplyRemarks ()!=null && !"".equals(entity.getApplyRemarks ().toString())? entity.getApplyRemarks ().toString():null),"apply_remarks", entity.getApplyRemarks ())
            .eq(StringUtils.isNotBlank(entity.getCreatedAt ()!=null && !"".equals(entity.getCreatedAt ().toString())? entity.getCreatedAt ().toString():null),"created_at", entity.getCreatedAt ())
            .eq(StringUtils.isNotBlank(entity.getUpdatedAt ()!=null && !"".equals(entity.getUpdatedAt ().toString())? entity.getUpdatedAt ().toString():null),"updated_at", entity.getUpdatedAt ())
            .eq(StringUtils.isNotBlank(entity.getAttach ()!=null && !"".equals(entity.getAttach ().toString())? entity.getAttach ().toString():null),"attach", entity.getAttach ())
            .eq(StringUtils.isNotBlank(entity.getReviewRemarks ()!=null && !"".equals(entity.getReviewRemarks ().toString())? entity.getReviewRemarks ().toString():null),"review_remarks", entity.getReviewRemarks ())
            .eq(StringUtils.isNotBlank(entity.getWelfareMatterApplicationId ()!=null && !"".equals(entity.getWelfareMatterApplicationId ().toString())? entity.getWelfareMatterApplicationId ().toString():null),"welfare_matter_application_id", entity.getWelfareMatterApplicationId ())
            .eq(StringUtils.isNotBlank(entity.getOperatorId ()!=null && !"".equals(entity.getOperatorId ().toString())? entity.getOperatorId ().toString():null),"operator_id", entity.getOperatorId ())
            .eq(StringUtils.isNotBlank(entity.getOperatorName ()!=null && !"".equals(entity.getOperatorName ().toString())? entity.getOperatorName ().toString():null),"operator_name", entity.getOperatorName ())
            .eq(StringUtils.isNotBlank(entity.getAuditorId ()!=null && !"".equals(entity.getAuditorId ().toString())? entity.getAuditorId ().toString():null),"auditor_id", entity.getAuditorId ())
            .eq(StringUtils.isNotBlank(entity.getAuditorName ()!=null && !"".equals(entity.getAuditorName ().toString())? entity.getAuditorName ().toString():null),"auditor_name", entity.getAuditorName ())
            .eq(StringUtils.isNotBlank(entity.getStatus ()!=null && !"".equals(entity.getStatus ().toString())? entity.getStatus ().toString():null),"status", entity.getStatus ())
            .eq(StringUtils.isNotBlank(entity.getDiagnosisCertificateImage ()!=null && !"".equals(entity.getDiagnosisCertificateImage ().toString())? entity.getDiagnosisCertificateImage ().toString():null),"diagnosis_certificate_image", entity.getDiagnosisCertificateImage ())
            .eq(StringUtils.isNotBlank(entity.getDiagnosisCertificateStartTime ()!=null && !"".equals(entity.getDiagnosisCertificateStartTime ().toString())? entity.getDiagnosisCertificateStartTime ().toString():null),"diagnosis_certificate_start_time", entity.getDiagnosisCertificateStartTime ())
            .eq(StringUtils.isNotBlank(entity.getDiagnosisCertificateEndTime ()!=null && !"".equals(entity.getDiagnosisCertificateEndTime ().toString())? entity.getDiagnosisCertificateEndTime ().toString():null),"diagnosis_certificate_end_time", entity.getDiagnosisCertificateEndTime ())
            .eq(StringUtils.isNotBlank(entity.getName ()!=null && !"".equals(entity.getName ().toString())? entity.getName ().toString():null),"name", entity.getName ())
            .eq(StringUtils.isNotBlank(entity.getSex ()!=null && !"".equals(entity.getSex ().toString())? entity.getSex ().toString():null),"sex", entity.getSex ())
            .eq(StringUtils.isNotBlank(entity.getBirthday ()!=null && !"".equals(entity.getBirthday ().toString())? entity.getBirthday ().toString():null),"birthday", entity.getBirthday ())
            .eq(StringUtils.isNotBlank(entity.getNationality ()!=null && !"".equals(entity.getNationality ().toString())? entity.getNationality ().toString():null),"nationality", entity.getNationality ())
            .eq(StringUtils.isNotBlank(entity.getIdCard ()!=null && !"".equals(entity.getIdCard ().toString())? entity.getIdCard ().toString():null),"id_card", entity.getIdCard ())
            .eq(StringUtils.isNotBlank(entity.getMobilePhone ()!=null && !"".equals(entity.getMobilePhone ().toString())? entity.getMobilePhone ().toString():null),"mobile_phone", entity.getMobilePhone ());
    }

}