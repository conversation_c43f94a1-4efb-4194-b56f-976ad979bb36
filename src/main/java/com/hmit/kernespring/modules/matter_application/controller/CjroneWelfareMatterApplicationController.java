package com.hmit.kernespring.modules.matter_application.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.ConvertUtils;
import com.hmit.kernespring.common.utils.DateUtils;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.cjrone.entity.*;
import com.hmit.kernespring.modules.cjrone.service.*;
import com.hmit.kernespring.modules.cjrone.service.impl.CjroneChildrenRehabilitationSubsidyServiceImpl;
import com.hmit.kernespring.modules.cjrone_bl.entity.*;
import com.hmit.kernespring.modules.cjrone_bl.service.*;
import com.hmit.kernespring.modules.data_management.entity.DataChildEducationSubsidyEntity;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.entity.DataResidentPensionInsuranceEntity;
import com.hmit.kernespring.modules.data_management.service.DataChildEducationSubsidyService;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import com.hmit.kernespring.modules.data_management.service.DataResidentPensionInsuranceService;
import com.hmit.kernespring.modules.matter_application.entity.CjroneWelfareMatterApplicationEntity;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.CjroneWelfareMatterApplicationService;
import com.hmit.kernespring.modules.matter_application.service.DisabilityCertificateApplicationService;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.*;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-10 09:42:23
 */
@RestController
@RequestMapping("matter_application/cjronewelfarematterapplication")
public class CjroneWelfareMatterApplicationController extends AbstractController {
    @Autowired
    private DataRehabilitationSubsidyCategoryService dataRehabilitationSubsidyCategoryService;
    @Autowired
    private CjroneLivingAllowanceService cjroneLivingAllowanceService;
    @Autowired
    private CjroneRehabilitationSubsidyService cjroneRehabilitationSubsidyService;
    @Autowired
    private CjroneNursingSubsidyService cjroneNursingSubsidyService;
    @Autowired
    private DataChildEducationSubsidyService dataChildEducationSubsidyService;
    @Autowired
    private CjroneWelfareMatterApplicationService cjroneWelfareMatterApplicationService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private CjroneSignatureService cjroneSignatureService;
    @Autowired
    private CjroneWelfareApplyDocService cjroneWelfareApplyDocService;
    @Autowired
    private CjroneResidentPensionInsuranceService cjroneResidentPensionInsuranceService;
    @Autowired
    private DataResidentPensionInsuranceService dataResidentPensionInsuranceService;
    @Autowired
    private CjroneEmploymentSubsidyService cjroneEmploymentSubsidyService;
    @Autowired
    private CjroneFamilyAccessibilityTransformationService cjroneFamilyAccessibilityTransformationService;
    @Autowired
    private CjroneComprehensiveMedicalInsuranceService cjroneComprehensiveMedicalInsuranceService;

    @Autowired
    private SysOssService sysOssService;
    @Autowired
    private CjroneTwoSubsidyStandardsService cjroneTwoSubsidyStandardsService;
    @Autowired
    private DisabilityCertificateApplicationService disabilityCertificateApplicationService;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;
    @Autowired
    private CjroneblLivingSubsidyService cjroneblLivingSubsidyService;
    @Autowired
    private CjroneblNursingSubsidyService cjroneblNursingSubsidyService;
    @Autowired
    private CjroneblLivingAllowanceService cjroneblLivingAllowanceService;
    @Autowired
    private CjroneblZgjbyanglaoService cjroneblZgjbyanglaoService;
    @Autowired
    private CjroneblZgjbyiliaoService cjroneblZgjbyiliaoService;
    @Autowired
    private CjroneblCxjmyanglaoService cjroneblCxjmyanglaoService;
    @Autowired
    private CjroneblCxjmyiliaoService cjroneblCxjmyiliaoService;
    @Autowired
    private CjroneblTemporaryAssistanceService cjroneblTemporaryAssistanceService;
    @Autowired
    private CjroneblRehabilitationSubsidyService cjroneblRehabilitationSubsidyService;
    @Autowired
    private CjroneblBusinessGrantService cjroneblBusinessGrantService;
    @Autowired
    private CjroneblCollegeeduService cjroneblCollegeeduService;
    @Autowired
    private CjroneblChildeduService cjroneblChildeduService;
    @Autowired
    private CjroneblHospitalizationAllowanceService cjroneblHospitalizationAllowanceService;
    @Autowired
    private CjroneblMedicalSupportService cjroneblMedicalSupportService;
    @Autowired
    private Love24Service love24Service;
    @Autowired
    private CjroneChildrenRehabilitationSubsidyServiceImpl cjroneChildrenRehabilitationSubsidyService;

    //<editor-fold> 北仑修改代码   申请惠残事项模块

    /**
     * 保存 惠残事项申请
     */
    @RequestMapping("/save")
    //@RequiresPermissions("matter_application:cjronewelfarematterapplication:save")
    public R save(@RequestBody CjroneWelfareMatterApplicationEntity cjroneWelfareMatterApplication){


        DataDisabilityCertificateEntity dataDisabilityCertificateEntity2=new DataDisabilityCertificateEntity();
        Map<String, Object> dis_tmp = new HashMap<>();
        dis_tmp.put("id_card", cjroneWelfareMatterApplication.getIdCard());
        List<DataDisabilityCertificateEntity> dis_list=(List<DataDisabilityCertificateEntity>) dataDisabilityCertificateService.listByMap(dis_tmp);
        if(dis_list!=null && dis_list.size()>0){
            // 验证为持证残疾人
            dataDisabilityCertificateEntity2=dis_list.get(0);
        }

        //保存新增人
        cjroneWelfareMatterApplication.setCreateId(getUserId());
        cjroneWelfareMatterApplication.setCreateName(getUser().getRoleName());
        //保存创建时间
        cjroneWelfareMatterApplication.setCreateTime(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN));
        cjroneWelfareMatterApplication.setApplicationTime(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN));
        // 保存申请类型为PC端
        cjroneWelfareMatterApplication.setApplyType(1);
        // 保存申请状态  1 申请人待手签
        if("1".equals(cjroneWelfareMatterApplication.getIsSnetkfxlApply())||"1".equals(cjroneWelfareMatterApplication.getIsJsbApply())){
            cjroneWelfareMatterApplication.setStatus("7");
        }else {
            cjroneWelfareMatterApplication.setStatus("1");
        }
        //获得当前登录用户的角色 （拥有保存权限的只有街道与村） --  电信 ，移动 ，等同于村
        if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("镇街道") != -1){
            cjroneWelfareMatterApplication.setNativeZhen(getUser().getZhen());
        }else if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("村") != -1){
            cjroneWelfareMatterApplication.setNativeCun(getUser().getCun());
            cjroneWelfareMatterApplication.setNativeZhen(getUser().getZhen());
        }else if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("电信") != -1){
            cjroneWelfareMatterApplication.setNativeCun(getUser().getCun());
            cjroneWelfareMatterApplication.setNativeZhen(dataDisabilityCertificateEntity2.getJiedao());
        }else if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("移动") != -1){
            cjroneWelfareMatterApplication.setNativeCun(getUser().getCun());
            cjroneWelfareMatterApplication.setNativeZhen(dataDisabilityCertificateEntity2.getJiedao());
        }


        cjroneWelfareMatterApplication.setCreateName(getUser().getName());
        //保存联系人信息 和 开户银行信息
        DataDisabilityCertificateEntity dataDisabilityCertificateEntity=dataDisabilityCertificateService.getByIDCard(cjroneWelfareMatterApplication.getIdCard());
        if(dataDisabilityCertificateEntity!=null){
            dataDisabilityCertificateEntity.setGuardianName(cjroneWelfareMatterApplication.getGuardianName());
            dataDisabilityCertificateEntity.setGuardianMobile(cjroneWelfareMatterApplication.getGuardianPhone());
            dataDisabilityCertificateEntity.setGuardianTelephone(cjroneWelfareMatterApplication.getGuardianPhone());
            dataDisabilityCertificateEntity.setGuardianRelation(cjroneWelfareMatterApplication.getGuardianRelation());
            dataDisabilityCertificateEntity.setBankName(cjroneWelfareMatterApplication.getBankName());
            dataDisabilityCertificateEntity.setBankAccount(cjroneWelfareMatterApplication.getBankAccount());
            dataDisabilityCertificateService.updateById(dataDisabilityCertificateEntity);
        }


        // 开启复杂的保存事项
        cjroneWelfareMatterApplicationService.save(cjroneWelfareMatterApplication);
        return R.ok();
    }

    /**
     * 生成电子pdf 并填充内容 用于申请人手签
     */
    @RequestMapping("/printThreePDF/{idCard}")
    public R printThreePDF(@PathVariable("idCard") String idCard) throws UnsupportedEncodingException {
        Map<String, Object> params = new HashMap<>();
        params.put("id_card",idCard);
        params.put("status",'1');  // 申请人待手签的状态
        // 获得残疾证信息
        DataDisabilityCertificateEntity dd=dataDisabilityCertificateService.getByIDCard(idCard);
        List<CjroneWelfareMatterApplicationEntity> matterApplicationEntities = (List<CjroneWelfareMatterApplicationEntity>) cjroneWelfareMatterApplicationService.listByMap(params);
        List<CjroneWelfareMatterApplicationEntity> result_list = new ArrayList<>();
        matterApplicationEntities.forEach(matterApplicationEntity -> {
            if ("生活补贴".equals(matterApplicationEntity.getMatterName())) {

                //根据编号获得生活补贴信息
                CjroneblLivingSubsidyEntity cjroneblLivingSubsidyEntity = cjroneblLivingSubsidyService.getById(matterApplicationEntity.getMatterId());

                Calendar now = Calendar.getInstance();

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"生活补助模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"living_allowance_"+cjroneblLivingSubsidyEntity.getIdCard()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", cjroneblLivingSubsidyEntity.getName()==null?"":cjroneblLivingSubsidyEntity.getName());
                if(cjroneblLivingSubsidyEntity.getSex()!=null){
                    if("0".equals(cjroneblLivingSubsidyEntity.getSex())){
                        map.put("sex","女");
                    }else{
                        map.put("sex","男");
                    }
                }else{
                    map.put("sex","");
                }
                map.put("birthday",cjroneblLivingSubsidyEntity.getBirthday()==null?"":cjroneblLivingSubsidyEntity.getBirthday());
                map.put("natinality",dd.getNationality()==null?"":dd.getNationality());
                map.put("nativeZhenName",dd.getJiedao()==null?"":dd.getJiedao());
                map.put("nativeCunName",dd.getShequ()==null?"":dd.getShequ());
                map.put("idCard",cjroneblLivingSubsidyEntity.getIdCard()==null?"":cjroneblLivingSubsidyEntity.getIdCard());
                map.put("disabilityType",cjroneblLivingSubsidyEntity.getDisabilityType()==null?"":cjroneblLivingSubsidyEntity.getDisabilityType());
                map.put("disabilityDegree",cjroneblLivingSubsidyEntity.getDisabilityDegree()==null?"":cjroneblLivingSubsidyEntity.getDisabilityDegree());
                map.put("disableId",cjroneblLivingSubsidyEntity.getDisableId()==null?"":cjroneblLivingSubsidyEntity.getDisableId());
                map.put("nativeAddress",cjroneblLivingSubsidyEntity.getNativeAddress()==null?"":cjroneblLivingSubsidyEntity.getNativeAddress());
                map.put("liveAddress",cjroneblLivingSubsidyEntity.getLiveAddress()==null?"":cjroneblLivingSubsidyEntity.getLiveAddress());
                map.put("telephone",cjroneblLivingSubsidyEntity.getTelephone()==null?"":cjroneblLivingSubsidyEntity.getTelephone());
                map.put("bankName",cjroneblLivingSubsidyEntity.getBankName()==null?"":cjroneblLivingSubsidyEntity.getBankName());
                map.put("bankAccount",cjroneblLivingSubsidyEntity.getBankAccount()==null?"":cjroneblLivingSubsidyEntity.getBankAccount());
                map.put("guardianName",cjroneblLivingSubsidyEntity.getGuardianName()==null?"":cjroneblLivingSubsidyEntity.getGuardianName());
                map.put("guardianPhone",cjroneblLivingSubsidyEntity.getGuardianPhone()==null?"":cjroneblLivingSubsidyEntity.getGuardianPhone());
                map.put("familyEconomy",cjroneblLivingSubsidyEntity.getFamilyEconomy()==null?"":cjroneblLivingSubsidyEntity.getFamilyEconomy());
                map.put("income",cjroneblLivingSubsidyEntity.getIncome()==null?"":cjroneblLivingSubsidyEntity.getIncome());
                map.put("applyDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
                map.put("zhen2","√");
                //map.put("mz2","√");
                //开始关联补助金额
                Map<String, Object> mapparams =new HashMap<String, Object>();
                mapparams.put("type","生活补贴");
                List<CjroneTwoSubsidyStandardsEntity> cjroneTwoSubsidyStandardsEntityList=cjroneTwoSubsidyStandardsService.queryByMap(mapparams);
                if(cjroneTwoSubsidyStandardsEntityList!=null&&cjroneTwoSubsidyStandardsEntityList.size()>0){
                    //map.put("subsidymoney",cjroneTwoSubsidyStandardsEntityList.get(0).getMoney().toString());
                }
                else{
                    //map.put("subsidymoney","无数据");
                }

                //map.put("subsidyMoney",cjroneblLivingSubsidyEntity.getSubsidyMoney()==null?"":cjroneblLivingSubsidyEntity.getSubsidyMoney());
                map.put("qcl2","√");


                FileOutputStream out;
                int num = 2;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        System.out.println("templatePath--->"+templatePath);
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/living_allowance_" + cjroneblLivingSubsidyEntity.getIdCard() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项生活补贴");
                cjroneSignature.setTypeId(cjroneblLivingSubsidyEntity.getId());
                cjroneSignature.setFileName("living_allowance_" + cjroneblLivingSubsidyEntity.getIdCard() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("护理补贴".equals(matterApplicationEntity.getMatterName())){

                //根据编号获得详细信息
                CjroneblNursingSubsidyEntity cjroneblNursingSubsidyEntity = cjroneblNursingSubsidyService.getById(matterApplicationEntity.getMatterId());

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"护理补助模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"nursing_subsidy_"+cjroneblNursingSubsidyEntity.getIdCard()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                Map<String, Object> mapparams =new HashMap<String, Object>();

                //获取年月日数据
                Calendar now = Calendar.getInstance();
                String nums[] = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};

                // 获得待生成的实体文件
                map.put("name", cjroneblNursingSubsidyEntity.getName());
                if (cjroneblNursingSubsidyEntity.getSex() == 1)
                    map.put("sex", "男");
                else
                    map.put("sex", "女");

                map.put("zhenName",dd.getJiedao()==null?"":dd.getJiedao());
                map.put("cunName",dd.getShequ()==null?"":dd.getShequ());
                map.put("birthday", cjroneblNursingSubsidyEntity.getBirthday()==null?"":cjroneblNursingSubsidyEntity.getBirthday());
                map.put("nationality", cjroneblNursingSubsidyEntity.getNationality()==null?"":cjroneblNursingSubsidyEntity.getNationality());
                map.put("idCard", cjroneblNursingSubsidyEntity.getIdCard()==null?"":cjroneblNursingSubsidyEntity.getIdCard());
                map.put("disabilityCategory", cjroneblNursingSubsidyEntity.getDisabilityCategory()==null?"":cjroneblNursingSubsidyEntity.getDisabilityCategory());
                map.put("disabilityDegree", cjroneblNursingSubsidyEntity.getDisabilityDegree()==null?"":cjroneblNursingSubsidyEntity.getDisabilityDegree());
                map.put("disableId", cjroneblNursingSubsidyEntity.getDisableId()==null?"":cjroneblNursingSubsidyEntity.getDisableId());
                map.put("nativeAddress", cjroneblNursingSubsidyEntity.getNativeAddress()==null?"":cjroneblNursingSubsidyEntity.getNativeAddress());
                map.put("liveAddress", cjroneblNursingSubsidyEntity.getLiveAddress()==null?"":cjroneblNursingSubsidyEntity.getLiveAddress());
                map.put("mobilePhone", cjroneblNursingSubsidyEntity.getMobilePhone()==null?"":cjroneblNursingSubsidyEntity.getMobilePhone());
                map.put("bankName", cjroneblNursingSubsidyEntity.getBankName()==null?"":cjroneblNursingSubsidyEntity.getBankName());
                map.put("bankAccount", cjroneblNursingSubsidyEntity.getBankAccount()==null?"":cjroneblNursingSubsidyEntity.getBankAccount());
                map.put("guardianName", cjroneblNursingSubsidyEntity.getGuardianName()==null?"":cjroneblNursingSubsidyEntity.getGuardianName());
                map.put("guardianPhone", cjroneblNursingSubsidyEntity.getGuardianPhone()==null?"":cjroneblNursingSubsidyEntity.getGuardianPhone());
                if("0".equals(cjroneblNursingSubsidyEntity.getSixMonth())){
                    map.put("sixMonth", "否");
                }else{
                    map.put("sixMonth", "是");
                }
                //map.put("sixMonth", cjroneblNursingSubsidyEntity.getSixMonth()==null?"":cjroneblNursingSubsidyEntity.getSixMonth());
                if("1".equals(cjroneblNursingSubsidyEntity.getCareType())){
                    map.put("careType","居家安养");
                } else if("2".equals(cjroneblNursingSubsidyEntity.getCareType())){
                    map.put("careType","日间照料");
                } else if("3".equals(cjroneblNursingSubsidyEntity.getCareType())){
                    map.put("careType","集中托养");
                } else{
                    map.put("careType","项目服务");
                }

                if("1".equals(cjroneblNursingSubsidyEntity.getLifeStatus())){
                    map.put("lifeStatus","生活完全不能自理");
                } else if ("2".equals(cjroneblNursingSubsidyEntity.getLifeStatus())){
                    map.put("lifeStatus","生活基本不能自理");
                } else{
                    map.put("lifeStatus","生活部分不能自理");
                }

                //map.put("careType",cjroneblNursingSubsidyEntity.getCareType()==null?"":cjroneblNursingSubsidyEntity.getCareType());
                //map.put("lifeStatus",cjroneblNursingSubsidyEntity.getLifeStatus()==null?"":cjroneblNursingSubsidyEntity.getLifeStatus());
                map.put("careMonths",cjroneblNursingSubsidyEntity.getCareMonths()==null?"":cjroneblNursingSubsidyEntity.getCareMonths().toString());
                map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
                map.put("actuallySubsidy",cjroneblNursingSubsidyEntity.getActuallySubsidy());
                map.put("f1","√");
                map.put("f2","√");

                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }


                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/nursing_subsidy_" + cjroneblNursingSubsidyEntity.getIdCard() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项护理补贴");
                cjroneSignature.setTypeId(cjroneblNursingSubsidyEntity.getId());
                cjroneSignature.setFileName("nursing_subsidy_" + cjroneblNursingSubsidyEntity.getIdCard() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("生活补助金".equals(matterApplicationEntity.getMatterName())){

                //根据编号获得生活补助金信息
                CjroneblLivingAllowanceEntity cjroneblLivingAllowanceEntity = cjroneblLivingAllowanceService.getById(matterApplicationEntity.getMatterId());

                Calendar now = Calendar.getInstance();

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"生活补助金模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"living_allowance_subsidy_"+cjroneblLivingAllowanceEntity.getIdCard()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", cjroneblLivingAllowanceEntity.getName()==null?"":cjroneblLivingAllowanceEntity.getName());
                if(cjroneblLivingAllowanceEntity.getSex()!=null){
                    if("0".equals(cjroneblLivingAllowanceEntity.getSex())){
                        map.put("sex","女");
                    }else{
                        map.put("sex","男");
                    }
                }else{
                    map.put("sex","");
                }
                map.put("birthday",cjroneblLivingAllowanceEntity.getBirthday()==null?"":cjroneblLivingAllowanceEntity.getBirthday());
                map.put("age",cjroneblLivingAllowanceEntity.getAge()==null?"":cjroneblLivingAllowanceEntity.getAge().toString());
                map.put("disabilityType",cjroneblLivingAllowanceEntity.getDisabilityType()==null?"":cjroneblLivingAllowanceEntity.getDisabilityType());
                map.put("disabilityDegree",cjroneblLivingAllowanceEntity.getDisabilityDegree()==null?"":cjroneblLivingAllowanceEntity.getDisabilityDegree());
                map.put("idCard",cjroneblLivingAllowanceEntity.getIdCard()==null?"":cjroneblLivingAllowanceEntity.getIdCard());
                map.put("disableId",cjroneblLivingAllowanceEntity.getDisableId()==null?"":cjroneblLivingAllowanceEntity.getDisableId());
                map.put("liveAddress",cjroneblLivingAllowanceEntity.getLiveAddress()==null?"":cjroneblLivingAllowanceEntity.getLiveAddress());
                map.put("telephone",cjroneblLivingAllowanceEntity.getTelephone()==null?"":cjroneblLivingAllowanceEntity.getTelephone());
                map.put("familyEconomy",cjroneblLivingAllowanceEntity.getFamilyEconomy()==null?"":cjroneblLivingAllowanceEntity.getFamilyEconomy());
                map.put("pension",cjroneblLivingAllowanceEntity.getPension()==null?"":cjroneblLivingAllowanceEntity.getPension());
                map.put("applyType",cjroneblLivingAllowanceEntity.getApplyType()==null?"":cjroneblLivingAllowanceEntity.getApplyType());
                map.put("subsidyMoney",cjroneblLivingAllowanceEntity.getSubsidyMoney()==null?"":cjroneblLivingAllowanceEntity.getSubsidyMoney());
                map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");


                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        System.out.println("templatePath--->"+templatePath);
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/living_allowance_subsidy_" + cjroneblLivingAllowanceEntity.getIdCard() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项生活补助金");
                cjroneSignature.setTypeId(cjroneblLivingAllowanceEntity.getId());
                cjroneSignature.setFileName("living_allowance_subsidy_" + cjroneblLivingAllowanceEntity.getIdCard() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("职工基本医疗保险补助".equals(matterApplicationEntity.getMatterName())){

                //根据编号获得职工基本医疗保险补助
                CjroneblZgjbyiliaoEntity cjroneblZgjbyiliaoEntity =cjroneblZgjbyiliaoService.getById(matterApplicationEntity.getMatterId());

                Calendar now = Calendar.getInstance();

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"职工基本医疗保险补助模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"zgyiliao_"+cjroneblZgjbyiliaoEntity.getDisableId()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", cjroneblZgjbyiliaoEntity.getName()==null?"":cjroneblZgjbyiliaoEntity.getName());
                if(cjroneblZgjbyiliaoEntity.getSex()!=null){
                    if(cjroneblZgjbyiliaoEntity.getSex()==0){
                        map.put("sex","女");
                    }else{
                        map.put("sex","男");
                    }
                }else{
                    map.put("sex","");
                }
                map.put("zhenName",dd.getJiedao()==null?"":dd.getJiedao());
                map.put("age",cjroneblZgjbyiliaoEntity.getAge()==null?"":cjroneblZgjbyiliaoEntity.getAge().toString());
                map.put("disableId",cjroneblZgjbyiliaoEntity.getDisableId()==null?"":cjroneblZgjbyiliaoEntity.getDisableId());
                map.put("telephone",cjroneblZgjbyiliaoEntity.getTelephone()==null?"":cjroneblZgjbyiliaoEntity.getTelephone());
                map.put("insuredStatus",cjroneblZgjbyiliaoEntity.getInsuredStatus()==null?"":cjroneblZgjbyiliaoEntity.getInsuredStatus());
                map.put("liveAddress",cjroneblZgjbyiliaoEntity.getLiveAddress()==null?"":cjroneblZgjbyiliaoEntity.getLiveAddress());
                map.put("otherSubsidy",cjroneblZgjbyiliaoEntity.getOtherSubsidy()==null?"":cjroneblZgjbyiliaoEntity.getOtherSubsidy());
                map.put("seTime2",cjroneblZgjbyiliaoEntity.getSeTime()==null?"":cjroneblZgjbyiliaoEntity.getSeTime());
                map.put("payMoney2",cjroneblZgjbyiliaoEntity.getPayMoney()==null?"":cjroneblZgjbyiliaoEntity.getPayMoney());
                map.put("insuredTypeY","基本医疗保险");
                map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
                map.put("subsidyMoney2",cjroneblZgjbyiliaoEntity.getSubsidyMoney()==null?"":cjroneblZgjbyiliaoEntity.getSubsidyMoney());


                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        System.out.println("templatePath--->"+templatePath);
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/zgyiliao_" + cjroneblZgjbyiliaoEntity.getDisableId() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项职工基本医疗保险补助");
                cjroneSignature.setTypeId(cjroneblZgjbyiliaoEntity.getId());
                cjroneSignature.setFileName("zgyiliao_" + cjroneblZgjbyiliaoEntity.getDisableId() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("职工基本养老保险补助".equals(matterApplicationEntity.getMatterName())){

                //根据编号获得职工基本养老保险补助
                CjroneblZgjbyanglaoEntity cjroneblZgjbyanglaoEntity =cjroneblZgjbyanglaoService.getById(matterApplicationEntity.getMatterId());

                Calendar now = Calendar.getInstance();

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"职工基本养老保险补助模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"zgyanglao_"+cjroneblZgjbyanglaoEntity.getDisableId()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", cjroneblZgjbyanglaoEntity.getName()==null?"":cjroneblZgjbyanglaoEntity.getName());
                if(cjroneblZgjbyanglaoEntity.getSex()!=null){
                    if(cjroneblZgjbyanglaoEntity.getSex()==0){
                        map.put("sex","女");
                    }else{
                        map.put("sex","男");
                    }
                }else{
                    map.put("sex","");
                }
                map.put("zhenName",dd.getJiedao()==null?"":dd.getJiedao());
                map.put("age",cjroneblZgjbyanglaoEntity.getAge()==null?"":cjroneblZgjbyanglaoEntity.getAge().toString());
                map.put("disableId",cjroneblZgjbyanglaoEntity.getDisableId()==null?"":cjroneblZgjbyanglaoEntity.getDisableId());
                map.put("telephone",cjroneblZgjbyanglaoEntity.getTelephone()==null?"":cjroneblZgjbyanglaoEntity.getTelephone());
                map.put("insuredStatus",cjroneblZgjbyanglaoEntity.getInsuredStatus()==null?"":cjroneblZgjbyanglaoEntity.getInsuredStatus());
                map.put("liveAddress",cjroneblZgjbyanglaoEntity.getLiveAddress()==null?"":cjroneblZgjbyanglaoEntity.getLiveAddress());
                map.put("otherSubsidy",cjroneblZgjbyanglaoEntity.getOtherSubsidy()==null?"":cjroneblZgjbyanglaoEntity.getOtherSubsidy());
                map.put("seTime1",cjroneblZgjbyanglaoEntity.getSeTime()==null?"":cjroneblZgjbyanglaoEntity.getSeTime());
                map.put("payMoney1",cjroneblZgjbyanglaoEntity.getPayMoney()==null?"":cjroneblZgjbyanglaoEntity.getPayMoney());
                map.put("insuredTypeYL","职工基本养老保险");
                map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
                map.put("subsidyMoney1",cjroneblZgjbyanglaoEntity.getSubsidyMoney()==null?"":cjroneblZgjbyanglaoEntity.getSubsidyMoney());


                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        System.out.println("templatePath--->"+templatePath);
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/zgyanglao_" + cjroneblZgjbyanglaoEntity.getDisableId() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项职工基本养老保险补助");
                cjroneSignature.setTypeId(cjroneblZgjbyanglaoEntity.getId());
                cjroneSignature.setFileName("zgyanglao_" + cjroneblZgjbyanglaoEntity.getDisableId() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("城乡居民养老保险补助".equals(matterApplicationEntity.getMatterName())){
                //根据编号获得城乡居民养老保险补助
                CjroneblCxjmyanglaoEntity cjroneblCxjmyanglaoEntity = cjroneblCxjmyanglaoService.getById(matterApplicationEntity.getMatterId());

                Calendar now = Calendar.getInstance();

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"城乡居民养老保险补助模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"cxyanglao_"+cjroneblCxjmyanglaoEntity.getDisableId()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", cjroneblCxjmyanglaoEntity.getName()==null?"":cjroneblCxjmyanglaoEntity.getName());
                if(cjroneblCxjmyanglaoEntity.getSex()!=null){
                    if(cjroneblCxjmyanglaoEntity.getSex()==0){
                        map.put("sex","女");
                    }else{
                        map.put("sex","男");
                    }
                }else{
                    map.put("sex","");
                }
                map.put("zhenName",dd.getJiedao()==null?"":dd.getJiedao());
                map.put("age",cjroneblCxjmyanglaoEntity.getAge()==null?"":cjroneblCxjmyanglaoEntity.getAge().toString());
                map.put("disableId",cjroneblCxjmyanglaoEntity.getDisableId()==null?"":cjroneblCxjmyanglaoEntity.getDisableId());
                map.put("telephone",cjroneblCxjmyanglaoEntity.getTelephone()==null?"":cjroneblCxjmyanglaoEntity.getTelephone());
                map.put("insuredStatus",cjroneblCxjmyanglaoEntity.getInsuredStatus()==null?"":cjroneblCxjmyanglaoEntity.getInsuredStatus());
                map.put("liveAddress",cjroneblCxjmyanglaoEntity.getLiveAddress()==null?"":cjroneblCxjmyanglaoEntity.getLiveAddress());
                map.put("otherSubsidy",cjroneblCxjmyanglaoEntity.getOtherSubsidy()==null?"":cjroneblCxjmyanglaoEntity.getOtherSubsidy());
                map.put("seTime1",cjroneblCxjmyanglaoEntity.getSeTime()==null?"":cjroneblCxjmyanglaoEntity.getSeTime());
                map.put("payMoney1",cjroneblCxjmyanglaoEntity.getPayMoney()==null?"":cjroneblCxjmyanglaoEntity.getPayMoney());
                map.put("insuredTypeYL","城乡居民养老保险");
                map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
                map.put("subsidyMoney1",cjroneblCxjmyanglaoEntity.getSubsidyMoney()==null?"":cjroneblCxjmyanglaoEntity.getSubsidyMoney());


                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        System.out.println("templatePath--->"+templatePath);
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/cxyanglao_" + cjroneblCxjmyanglaoEntity.getDisableId() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项城乡居民养老保险补助");
                cjroneSignature.setTypeId(cjroneblCxjmyanglaoEntity.getId());
                cjroneSignature.setFileName("zgyanglao_" + cjroneblCxjmyanglaoEntity.getDisableId() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("城乡基本医疗保险补助".equals(matterApplicationEntity.getMatterName())){

                //根据编号获得职工基本医疗保险补助
                CjroneblCxjmyiliaoEntity cjroneblCxjmyiliaoEntity =cjroneblCxjmyiliaoService.getById(matterApplicationEntity.getMatterId());

                Calendar now = Calendar.getInstance();

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"城乡基本医疗保险补助模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"cxyiliao_"+cjroneblCxjmyiliaoEntity.getDisableId()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", cjroneblCxjmyiliaoEntity.getName()==null?"":cjroneblCxjmyiliaoEntity.getName());
                if(cjroneblCxjmyiliaoEntity.getSex()!=null){
                    if(cjroneblCxjmyiliaoEntity.getSex()==0){
                        map.put("sex","女");
                    }else{
                        map.put("sex","男");
                    }
                }else{
                    map.put("sex","");
                }
                map.put("zhenName",dd.getJiedao()==null?"":dd.getJiedao());
                map.put("age",cjroneblCxjmyiliaoEntity.getAge()==null?"":cjroneblCxjmyiliaoEntity.getAge().toString());
                map.put("disableId",cjroneblCxjmyiliaoEntity.getDisableId()==null?"":cjroneblCxjmyiliaoEntity.getDisableId());
                map.put("telephone",cjroneblCxjmyiliaoEntity.getTelephone()==null?"":cjroneblCxjmyiliaoEntity.getTelephone());
                map.put("insuredStatus",cjroneblCxjmyiliaoEntity.getInsuredStatus()==null?"":cjroneblCxjmyiliaoEntity.getInsuredStatus());
                map.put("liveAddress",cjroneblCxjmyiliaoEntity.getLiveAddress()==null?"":cjroneblCxjmyiliaoEntity.getLiveAddress());
                map.put("otherSubsidy",cjroneblCxjmyiliaoEntity.getOtherSubsidy()==null?"":cjroneblCxjmyiliaoEntity.getOtherSubsidy());
                map.put("seTime2",cjroneblCxjmyiliaoEntity.getSeTime()==null?"":cjroneblCxjmyiliaoEntity.getSeTime());
                map.put("payMoney2",cjroneblCxjmyiliaoEntity.getPayMoney()==null?"":cjroneblCxjmyiliaoEntity.getPayMoney());
                map.put("insuredTypeY","城乡居民基本医疗保险");
                map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
                map.put("subsidyMoney2",cjroneblCxjmyiliaoEntity.getSubsidyMoney()==null?"":cjroneblCxjmyiliaoEntity.getSubsidyMoney());


                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        System.out.println("templatePath--->"+templatePath);
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/cxyiliao_" + cjroneblCxjmyiliaoEntity.getDisableId() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项城乡基本医疗保险补助");
                cjroneSignature.setTypeId(cjroneblCxjmyiliaoEntity.getId());
                cjroneSignature.setFileName("cxyiliao_" + cjroneblCxjmyiliaoEntity.getDisableId() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("残疾人临时救助".equals(matterApplicationEntity.getMatterName())){

                //根据编号获得残疾人临时救助
                CjroneblTemporaryAssistanceEntity cjroneblTemporaryAssistanceEntity =cjroneblTemporaryAssistanceService.getById(matterApplicationEntity.getMatterId());

                Calendar now = Calendar.getInstance();

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"残疾人临时救助模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"temporary_assistance_"+cjroneblTemporaryAssistanceEntity.getIdCard()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", cjroneblTemporaryAssistanceEntity.getName()==null?"":cjroneblTemporaryAssistanceEntity.getName());
                if(cjroneblTemporaryAssistanceEntity.getSex()!=null){
                    if("0".equals(cjroneblTemporaryAssistanceEntity.getSex())){
                        map.put("sex","女");
                    }else{
                        map.put("sex","男");
                    }
                }else{
                    map.put("sex","");
                }
                map.put("disabilityType",cjroneblTemporaryAssistanceEntity.getDisabilityType()==null?"":cjroneblTemporaryAssistanceEntity.getDisabilityType());
                map.put("disabilityDegree",cjroneblTemporaryAssistanceEntity.getDisabilityDegree()==null?"":cjroneblTemporaryAssistanceEntity.getDisabilityDegree());
                map.put("disableId",cjroneblTemporaryAssistanceEntity.getDisableId()==null?"":cjroneblTemporaryAssistanceEntity.getDisableId());
                map.put("telephone",cjroneblTemporaryAssistanceEntity.getTelephone()==null?"":cjroneblTemporaryAssistanceEntity.getTelephone());
                map.put("liveAddress",cjroneblTemporaryAssistanceEntity.getLiveAddress()==null?"":cjroneblTemporaryAssistanceEntity.getLiveAddress());
                map.put("familyEconomy",cjroneblTemporaryAssistanceEntity.getFamilyEconomy()==null?"":cjroneblTemporaryAssistanceEntity.getFamilyEconomy());
                if("1".equals(cjroneblTemporaryAssistanceEntity.getMingzhenSubsidy())){
                    map.put("mingzhenSubsidy","是");
                }else{
                    map.put("mingzhenSubsidy","否");
                }
                map.put("idCard",cjroneblTemporaryAssistanceEntity.getIdCard()==null?"":cjroneblTemporaryAssistanceEntity.getIdCard());
                map.put("birthday",cjroneblTemporaryAssistanceEntity.getBirthday()==null?"":cjroneblTemporaryAssistanceEntity.getBirthday());
                map.put("payMoney",cjroneblTemporaryAssistanceEntity.getPayMoney()==null?"":cjroneblTemporaryAssistanceEntity.getPayMoney());
                map.put("subsidyMoney",cjroneblTemporaryAssistanceEntity.getSubsidyMoney()==null?"":cjroneblTemporaryAssistanceEntity.getSubsidyMoney());
                map.put("applyReason",cjroneblTemporaryAssistanceEntity.getApplyReason()==null?"":cjroneblTemporaryAssistanceEntity.getApplyReason());
                map.put("applyDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        System.out.println("templatePath--->"+templatePath);
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/temporary_assistance_" + cjroneblTemporaryAssistanceEntity.getIdCard() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项残疾人临时救助");
                cjroneSignature.setTypeId(cjroneblTemporaryAssistanceEntity.getId());
                cjroneSignature.setFileName("temporary_assistance_" + cjroneblTemporaryAssistanceEntity.getIdCard() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("康复补助".equals(matterApplicationEntity.getMatterName())){

                //根据编号获得残疾人临时救助
                CjroneblRehabilitationSubsidyEntity cjroneblRehabilitationSubsidyEntity =cjroneblRehabilitationSubsidyService.getById(matterApplicationEntity.getMatterId());

                Calendar now = Calendar.getInstance();

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"康复补助模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"rehabilitation_subsidy_"+cjroneblRehabilitationSubsidyEntity.getIdCard()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", cjroneblRehabilitationSubsidyEntity.getName()==null?"":cjroneblRehabilitationSubsidyEntity.getName());
                if(cjroneblRehabilitationSubsidyEntity.getSex()!=null){
                    if("0".equals(cjroneblRehabilitationSubsidyEntity.getSex())){
                        map.put("sex","女");
                    }else{
                        map.put("sex","男");
                    }
                }else{
                    map.put("sex","");
                }
                map.put("birthday",cjroneblRehabilitationSubsidyEntity.getBirthday()==null?"":cjroneblRehabilitationSubsidyEntity.getBirthday());
                map.put("nationality",cjroneblRehabilitationSubsidyEntity.getNationailty()==null?"":cjroneblRehabilitationSubsidyEntity.getNationailty());
                map.put("disabilityType",cjroneblRehabilitationSubsidyEntity.getDisabilityType()==null?"":cjroneblRehabilitationSubsidyEntity.getDisabilityType());
                map.put("disabilityDegree",cjroneblRehabilitationSubsidyEntity.getDisabilityDegree()==null?"":cjroneblRehabilitationSubsidyEntity.getDisabilityDegree());
                map.put("disableId",cjroneblRehabilitationSubsidyEntity.getDisabileId()==null?"":cjroneblRehabilitationSubsidyEntity.getDisabileId());
                map.put("idCard",cjroneblRehabilitationSubsidyEntity.getIdCard()==null?"":cjroneblRehabilitationSubsidyEntity.getIdCard());
                map.put("telephone",cjroneblRehabilitationSubsidyEntity.getTelephone()==null?"":cjroneblRehabilitationSubsidyEntity.getTelephone());
                map.put("liveAddress",cjroneblRehabilitationSubsidyEntity.getLiveAddress()==null?"":cjroneblRehabilitationSubsidyEntity.getLiveAddress());
                map.put("familyEconomy",cjroneblRehabilitationSubsidyEntity.getFamilyEconomy()==null?"":cjroneblRehabilitationSubsidyEntity.getFamilyEconomy());
                map.put("ylbx",cjroneblRehabilitationSubsidyEntity.getYlbx()==null?"":cjroneblRehabilitationSubsidyEntity.getYlbx());
                map.put("guardianTelephone",cjroneblRehabilitationSubsidyEntity.getGuardianTelephone()==null?"":cjroneblRehabilitationSubsidyEntity.getGuardianTelephone());
                map.put("guardianName",cjroneblRehabilitationSubsidyEntity.getGuardianName()==null?"":cjroneblRehabilitationSubsidyEntity.getGuardianName());
                map.put("applyDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        System.out.println("templatePath--->"+templatePath);
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/rehabilitation_subsidy_" + cjroneblRehabilitationSubsidyEntity.getIdCard() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项康复补助");
                cjroneSignature.setTypeId(cjroneblRehabilitationSubsidyEntity.getId());
                cjroneSignature.setFileName("rehabilitation_subsidy_" + cjroneblRehabilitationSubsidyEntity.getIdCard() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("创业补助".equals(matterApplicationEntity.getMatterName())){

                //根据编号获得残疾人创业补助
                CjroneblBusinessGrantEntity cjroneblBusinessGrantEntity =cjroneblBusinessGrantService.getById(matterApplicationEntity.getMatterId());

                Calendar now = Calendar.getInstance();

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"创业模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"business_grant_"+cjroneblBusinessGrantEntity.getDisableId()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", cjroneblBusinessGrantEntity.getName()==null?"":cjroneblBusinessGrantEntity.getName());
                if(cjroneblBusinessGrantEntity.getSex()!=null){
                    if("0".equals(cjroneblBusinessGrantEntity.getSex())){
                        map.put("sex","女");
                    }else{
                        map.put("sex","男");
                    }
                }else{
                    map.put("sex","");
                }
                map.put("age",cjroneblBusinessGrantEntity.getAge()==null?"":cjroneblBusinessGrantEntity.getAge().toString());
                map.put("disableId",cjroneblBusinessGrantEntity.getDisableId()==null?"":cjroneblBusinessGrantEntity.getDisableId());
                map.put("telephone",cjroneblBusinessGrantEntity.getTelephone()==null?"":cjroneblBusinessGrantEntity.getTelephone());
                map.put("familyEconomy",cjroneblBusinessGrantEntity.getFamilyEconomy()==null?"":cjroneblBusinessGrantEntity.getFamilyEconomy());
                map.put("educationDegree",cjroneblBusinessGrantEntity.getEducationDegree()==null?"":cjroneblBusinessGrantEntity.getEducationDegree());
                map.put("liveAddress",cjroneblBusinessGrantEntity.getLiveAddress()==null?"":cjroneblBusinessGrantEntity.getLiveAddress());
                map.put("startTime",cjroneblBusinessGrantEntity.getStartTime()==null?"":cjroneblBusinessGrantEntity.getStartTime());
                map.put("manageAddress",cjroneblBusinessGrantEntity.getManageAddress()==null?"":cjroneblBusinessGrantEntity.getManageAddress());
                map.put("manageRange",cjroneblBusinessGrantEntity.getManageRange()==null?"":cjroneblBusinessGrantEntity.getManageRange());

                if("0".equals(cjroneblBusinessGrantEntity.getIsEmployment())){
                    map.put("isEmployment","否");
                }else{
                    map.put("isEmployment","是");
                }

                if("0".equals(cjroneblBusinessGrantEntity.getIsFirsttime())){
                    map.put("isFirsttime","否");
                }else{
                    map.put("isFirsttime","是");
                }

                if("0".equals(cjroneblBusinessGrantEntity.getIsSixManage())){
                    map.put("isSixManage","否");
                }else{
                    map.put("isSixManage","是");
                    map.put("subsidyMoney","6000");
                }

                if("0".equals(cjroneblBusinessGrantEntity.getIsCollege())){
                    map.put("isCollege","否");
                }else{
                    map.put("isCollege","是");
                    map.put("subsidyMoney","10000");
                }


                //map.put("subsidyMoney",cjroneblBusinessGrantEntity.getSubsidyMoney()==null?"":cjroneblBusinessGrantEntity.getSubsidyMoney());
                map.put("subsidyReason",cjroneblBusinessGrantEntity.getSubsidyReason()==null?"":cjroneblBusinessGrantEntity.getSubsidyReason());
                map.put("applyDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        System.out.println("templatePath--->"+templatePath);
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/business_grant_" + cjroneblBusinessGrantEntity.getDisableId() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项创业补助");
                cjroneSignature.setTypeId(cjroneblBusinessGrantEntity.getId());
                cjroneSignature.setFileName("business_grant_" + cjroneblBusinessGrantEntity.getDisableId() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("大学生补助".equals(matterApplicationEntity.getMatterName())){

                //根据编号获得大学生补助
                CjroneblCollegeeduEntity cjroneblCollegeeduEntity =cjroneblCollegeeduService.getById(matterApplicationEntity.getMatterId());

                Calendar now = Calendar.getInstance();

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"大学生补助模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"college_edu_"+cjroneblCollegeeduEntity.getDisableId()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", cjroneblCollegeeduEntity.getName()==null?"":cjroneblCollegeeduEntity.getName());
                if(cjroneblCollegeeduEntity.getSex()!=null){
                    if("0".equals(cjroneblCollegeeduEntity.getSex())){
                        map.put("sex","女");
                    }else{
                        map.put("sex","男");
                    }
                }else{
                    map.put("sex","");
                }

                if(cjroneblCollegeeduEntity.getHukouNature()!=null){
                    if("0".equals(cjroneblCollegeeduEntity.getHukouNature())){
                        map.put("hukouNature","非农业");
                    }else{
                        map.put("hukouNature","农业");
                    }
                }

                map.put("birthday",cjroneblCollegeeduEntity.getBirthday()==null?"":cjroneblCollegeeduEntity.getBirthday());
                map.put("collegeName",cjroneblCollegeeduEntity.getCollegeName()==null?"":cjroneblCollegeeduEntity.getCollegeName());
                map.put("majorName",cjroneblCollegeeduEntity.getMajorName()==null?"":cjroneblCollegeeduEntity.getMajorName());
                map.put("collegeTime",cjroneblCollegeeduEntity.getCollegeTime()==null?"":cjroneblCollegeeduEntity.getCollegeTime());
                map.put("disableId",cjroneblCollegeeduEntity.getDisableId()==null?"":cjroneblCollegeeduEntity.getDisableId());
                map.put("tuition",cjroneblCollegeeduEntity.getTuition()==null?"":cjroneblCollegeeduEntity.getTuition());
                map.put("actuallyTuition",cjroneblCollegeeduEntity.getActuallyTuition()==null?"":cjroneblCollegeeduEntity.getActuallyTuition());
                map.put("accommodationFee",cjroneblCollegeeduEntity.getAccommodationFee()==null?"":cjroneblCollegeeduEntity.getAccommodationFee());
                map.put("actuallyAccommodationFee",cjroneblCollegeeduEntity.getActuallyAccommodationFee()==null?"":cjroneblCollegeeduEntity.getActuallyAccommodationFee());
                // map.put("hukouNature",cjroneblCollegeeduEntity.getHukouNature()==null?"":cjroneblCollegeeduEntity.getHukouNature());
                map.put("familyCount",cjroneblCollegeeduEntity.getFamilyCount()==null?"":cjroneblCollegeeduEntity.getFamilyCount());
                map.put("liveAddress",cjroneblCollegeeduEntity.getLiveAddress()==null?"":cjroneblCollegeeduEntity.getLiveAddress());
                map.put("telephone",cjroneblCollegeeduEntity.getTelephone()==null?"":cjroneblCollegeeduEntity.getTelephone());
                map.put("postcode",cjroneblCollegeeduEntity.getPostcode()==null?"":cjroneblCollegeeduEntity.getPostcode());
                map.put("familyFinances",cjroneblCollegeeduEntity.getFamilyFinances()==null?"":cjroneblCollegeeduEntity.getFamilyFinances());
                map.put("familyIncome",cjroneblCollegeeduEntity.getFamilyIncome()==null?"":cjroneblCollegeeduEntity.getFamilyIncome());
                map.put("subsidyReason",cjroneblCollegeeduEntity.getSubsidyReason()==null?"":cjroneblCollegeeduEntity.getSubsidyReason());


                map.put("applyDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        System.out.println("templatePath--->"+templatePath);
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/college_edu_" + cjroneblCollegeeduEntity.getDisableId() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项大学生补助");
                cjroneSignature.setTypeId(cjroneblCollegeeduEntity.getId());
                cjroneSignature.setFileName("college_edu_" + cjroneblCollegeeduEntity.getDisableId() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("医疗救助".equals(matterApplicationEntity.getMatterName())){
                //根据编号获得医疗救助
                CjroneblMedicalSupportEntity cjroneblMedicalSupportEntity =cjroneblMedicalSupportService.getById(matterApplicationEntity.getMatterId());

                Calendar now = Calendar.getInstance();

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"大病和精神病审批表.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"yljz_"+cjroneblMedicalSupportEntity.getDisableId()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", cjroneblMedicalSupportEntity.getName()==null?"":cjroneblMedicalSupportEntity.getName());
                if(cjroneblMedicalSupportEntity.getSex()!=null){
                    if("0".equals(cjroneblMedicalSupportEntity.getSex())){
                        map.put("sex","女");
                    }else{
                        map.put("sex","男");
                    }
                }else{
                    map.put("sex","");
                }

                map.put("birthday",cjroneblMedicalSupportEntity.getBirthday()==null?"":cjroneblMedicalSupportEntity.getBirthday());
                map.put("disabilityId",cjroneblMedicalSupportEntity.getDisableId()==null?"":cjroneblMedicalSupportEntity.getDisableId());
                map.put("zyTime",cjroneblMedicalSupportEntity.getTelephone()==null?"":cjroneblMedicalSupportEntity.getTelephone());
                map.put("liveAddress",cjroneblMedicalSupportEntity.getLiveAddress()==null?"":cjroneblMedicalSupportEntity.getLiveAddress());
                map.put("telephone",cjroneblMedicalSupportEntity.getTelephone()==null?"":cjroneblMedicalSupportEntity.getTelephone());
                map.put("guardianName",cjroneblMedicalSupportEntity.getGuardianName()==null?"":cjroneblMedicalSupportEntity.getGuardianName());

                map.put("applyDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        System.out.println("templatePath--->"+templatePath);
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/yljz_" + cjroneblMedicalSupportEntity.getDisableId() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项医疗救助");
                cjroneSignature.setTypeId(cjroneblMedicalSupportEntity.getId());
                cjroneSignature.setFileName("yljz_" + cjroneblMedicalSupportEntity.getDisableId() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("住院补助".equals(matterApplicationEntity.getMatterName())){
                //根据编号获得住院补助
                CjroneblHospitalizationAllowanceEntity cjroneblHospitalizationAllowanceEntity =cjroneblHospitalizationAllowanceService.getById(matterApplicationEntity.getMatterId());

                Calendar now = Calendar.getInstance();

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"大病和精神病审批表.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"zybz_"+cjroneblHospitalizationAllowanceEntity.getDisableId()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", cjroneblHospitalizationAllowanceEntity.getName()==null?"":cjroneblHospitalizationAllowanceEntity.getName());
                if(cjroneblHospitalizationAllowanceEntity.getSex()!=null){
                    if("0".equals(cjroneblHospitalizationAllowanceEntity.getSex())){
                        map.put("sex","女");
                    }else{
                        map.put("sex","男");
                    }
                }else{
                    map.put("sex","");
                }

                map.put("birthday",cjroneblHospitalizationAllowanceEntity.getBirthday()==null?"":cjroneblHospitalizationAllowanceEntity.getBirthday());
                map.put("disabilityId",cjroneblHospitalizationAllowanceEntity.getDisableId()==null?"":cjroneblHospitalizationAllowanceEntity.getDisableId());
                map.put("zyTime",cjroneblHospitalizationAllowanceEntity.getTelephone()==null?"":cjroneblHospitalizationAllowanceEntity.getTelephone());
                map.put("liveAddress",cjroneblHospitalizationAllowanceEntity.getLiveAddress()==null?"":cjroneblHospitalizationAllowanceEntity.getLiveAddress());
                map.put("telephone",cjroneblHospitalizationAllowanceEntity.getTelephone()==null?"":cjroneblHospitalizationAllowanceEntity.getTelephone());
                map.put("guardianName",cjroneblHospitalizationAllowanceEntity.getGuardianName()==null?"":cjroneblHospitalizationAllowanceEntity.getGuardianName());

                map.put("applyDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        System.out.println("templatePath--->"+templatePath);
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/zybz_" + cjroneblHospitalizationAllowanceEntity.getDisableId() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项住院补助");
                cjroneSignature.setTypeId(cjroneblHospitalizationAllowanceEntity.getId());
                cjroneSignature.setFileName("zybz_" + cjroneblHospitalizationAllowanceEntity.getDisableId() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if("智慧爱心24小时".equals(matterApplicationEntity.getMatterName())){
                // 根据编号获得智慧爱心24小时
                Love24Entity love24Entity = love24Service.getById(matterApplicationEntity.getMatterId());

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"智慧爱心24小时.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"love24_"+love24Entity.getDisableId()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", love24Entity.getName()==null?"":love24Entity.getName());
                if(love24Entity.getSex()!=null){
                    if("0".equals(love24Entity.getSex())){
                        map.put("sex","女");
                    }else{
                        map.put("sex","男");
                    }
                }else{
                    map.put("sex","");
                }

                map.put("disabilityId",love24Entity.getDisableId()==null?"":love24Entity.getDisableId());
                map.put("disableType",love24Entity.getDisableType()==null?"":love24Entity.getDisableType());
                map.put("guardian",love24Entity.getGuardian()==null?"":love24Entity.getGuardian());
                map.put("liveAddress",love24Entity.getLiveAddress()==null?"":love24Entity.getLiveAddress());
                map.put("telephone",love24Entity.getTelephone()==null?"":love24Entity.getTelephone());

                map.put("relation1",love24Entity.getRelation1() ==null?"":love24Entity.getRelation1());
                map.put("name1",love24Entity.getName1()==null?"":love24Entity.getName1());
                map.put("idcard1",love24Entity.getIdcard1()==null?"":love24Entity.getIdcard1());
                map.put("tel1",love24Entity.getTel1()==null?"":love24Entity.getTel1());
                map.put("relation2",love24Entity.getRelation2() ==null?"":love24Entity.getRelation2());
                map.put("name2",love24Entity.getName2()==null?"":love24Entity.getName2());
                map.put("idcard2",love24Entity.getIdcard2()==null?"":love24Entity.getIdcard2());
                map.put("tel2",love24Entity.getTel2()==null?"":love24Entity.getTel2());
                map.put("relation3",love24Entity.getRelation3() ==null?"":love24Entity.getRelation3());
                map.put("name3",love24Entity.getName3()==null?"":love24Entity.getName3());
                map.put("idcard3",love24Entity.getIdcard3()==null?"":love24Entity.getIdcard3());
                map.put("tel3",love24Entity.getTel3()==null?"":love24Entity.getTel3());
                map.put("relation4",love24Entity.getRelation4() ==null?"":love24Entity.getRelation4());
                map.put("name4",love24Entity.getName4()==null?"":love24Entity.getName4());
                map.put("idcard4",love24Entity.getIdcard4()==null?"":love24Entity.getIdcard4());
                map.put("tel4",love24Entity.getTel4()==null?"":love24Entity.getTel4());

                // 判断是固定电话还是智能手机？
                if(love24Entity.getType()!=null && StringUtils.isNotBlank(love24Entity.getType())){

                    // 移动申请项目都为多选
                    if(love24Entity.getType().contains(",") || Integer.parseInt(love24Entity.getType())>= 10){
                        String [] typeLists =love24Entity.getType().split(",");
                        map.put("yidong0","");
                        map.put("yidong1","");
                        map.put("yidong2","");
                        map.put("yidong3","");

                        Arrays.stream(typeLists).forEach(item ->{
                            System.out.println("移动申请项目值： "+item);
                            //初始化设置
                            if("10".equals(item)){
                                //移动 智能手机定位
                                map.put("yidong0","√");
                            }
                            if("11".equals(item)){
                                //移动 购机优惠
                                map.put("yidong1","√");
                            }
                            if("12".equals(item)){
                                //移动 套餐优惠
                                map.put("yidong2","√");
                            }
                            if("13".equals(item)){
                                //移动 宽带电视
                                map.put("yidong3","√");
                            }
                        });
                        map.put("dianxin0","");
                        map.put("dianxin1","");
                        map.put("dianxin2","");
                        map.put("dianxin3","");
                        map.put("dianxin4","");
                        map.put("dianxinTelphone0","");
                        map.put("dianxinTelphone1","");
                        map.put("yidongTelphone",love24Entity.getTypeTelephone2()==null?"":love24Entity.getTypeTelephone2());

                    }

                    // 电信为单选，且值都小于10
                    if(!love24Entity.getType().contains(",") && Integer.parseInt(love24Entity.getType())<10 ){
                        System.out.println("电信固定电话："+love24Entity.getDianxinTelephone());
                        map.put("dianxin0","");
                        map.put("dianxin1","");
                        map.put("dianxin2","");
                        map.put("dianxin3","");
                        map.put("dianxin4","");

                        if("0".equals(love24Entity.getType())){
                            //电信  固定电话 + 手机
                            map.put("dianxin0","√");
                        }
                        if("1".equals(love24Entity.getType())){
                            //电信 CMDA手机一部
                            map.put("dianxin1","√");
                       }
                        if("2".equals(love24Entity.getType())){
                            //电信 固定电话
                            map.put("dianxin2","√");
                        }
                        if("4".equals(love24Entity.getType())){
                            //电信 但手机用户
                            map.put("dianxin4","√");

                        }
                        if("3".equals(love24Entity.getType())){
                            //电信 号码定位
                            map.put("dianxin3","√");
                        }
                        map.put("yidong0","");
                        map.put("yidong1","");
                        map.put("yidong2","");
                        map.put("yidong3","");
                        map.put("yidongTelphone","");
                        map.put("dianxinTelphone1",love24Entity.getDianxinTelephone()==null?"":love24Entity.getDianxinTelephone());
                        map.put("dianxinTelphone0",love24Entity.getTypeTelephone()==null?"":love24Entity.getTypeTelephone());

                    }
                }else{
                    map.put("dianxin0","");
                    map.put("dianxin1","");
                    map.put("dianxin2","");
                    map.put("dianxin3","");
                    map.put("dianxin4","");
                    map.put("yidong0","");
                    map.put("yidong1","");
                    map.put("yidong2","");
                    map.put("yidong3","");
                    map.put("dianxinTelphone0","");
                    map.put("dianxinTelphone1","");
                    map.put("yidongTelphone","");
                }

                // 电信是否勾选了号码定位功能
                if(love24Entity.getFamilyLove()!=null){
                    if("true".equals(love24Entity.getFamilyLove())){
                        map.put("dianxin3","√");
                    }
                }

                // 申请时间
                LocalDate today = LocalDate.now();
                map.put("applyDate",today.getYear()+"年"+today.getMonthValue() +"月"+ today.getDayOfMonth() +"日");

                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        System.out.println("templatePath--->"+templatePath);
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                    System.out.println(e);
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/love24_" + love24Entity.getDisableId() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项智慧爱心24小时");
                cjroneSignature.setTypeId(love24Entity.getId());
                cjroneSignature.setFileName("love24_" + love24Entity.getDisableId() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);


            }

        });
        CjroneWelfareMatterApplicationEntity welfareMatterApplicationEntity = new CjroneWelfareMatterApplicationEntity();
        if (result_list.size() != 0){
            welfareMatterApplicationEntity = result_list.get(0);
        }
        return R.ok().put("signTotal",result_list.size()).put("matterId",welfareMatterApplicationEntity.getMatterId()).put("matterName",welfareMatterApplicationEntity.getMatterName()).put("fileUrl",welfareMatterApplicationEntity.getPhoto());
    }


    /**
     * 列表 （这个列表仅仅就 村社区和镇街道使用，只需要区分这两个角色）
     */
    @RequestMapping("/list")
    //@RequiresPermissions("matter_application:cjronewelfarematterapplication:list")
    public R list(@RequestParam Map<String, Object> params){
        Map<String, Object> tmp = new Gson().fromJson(params.get("key").toString(),Map.class);
        System.out.println(new Gson().toJson(getUser()));
        if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("镇街道") != -1){
            tmp.put("nativeZhen",getUser().getZhen());
            params.put("usertype",2);  // 标注用户类型为街道
        }else if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("村") != -1){
            tmp.put("nativeCun",getUser().getCun());
            params.put("usertype",1);  // 标注用户类型为社区
        }else if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("电信") != -1){
            tmp.put("nativeCun",getUser().getCun());
            params.put("usertype",1);  // 标注用户类型为社区
        }else if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("移动") != -1){
            tmp.put("nativeCun",getUser().getCun());
            params.put("usertype",1);  // 标注用户类型为社区
        }

        params.put("key",new Gson().toJson(tmp));
        PageUtils page = cjroneWelfareMatterApplicationService.queryPage(params);

        return R.ok().put("page", page);
    }


    /*
      获得历史申请数据
     */
    @RequestMapping("/listrecord")
    //@RequiresPermissions("matter_application:cjronewelfarematterapplication:list")
    public R listrecord(@RequestParam Map<String, Object> params){
        Map<String, Object> tmp = new Gson().fromJson(params.get("key").toString(),Map.class);
        System.out.println(new Gson().toJson(getUser()));
        if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("镇街道") != -1){
            tmp.put("nativeZhen",getUser().getZhen());
            params.put("usertype",2);  // 标注用户类型为街道
        }else if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("村") != -1){
            tmp.put("nativeCun",getUser().getCun());
            params.put("usertype",1);  // 标注用户类型为社区
        }else if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("电信") != -1){
            tmp.put("nativeCun",getUser().getCun());
            params.put("usertype",1);  // 标注用户类型为社区
        }else if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("移动") != -1){
            tmp.put("nativeCun",getUser().getCun());
            params.put("usertype",1);  // 标注用户类型为社区
        }


        params.put("key",new Gson().toJson(tmp));
        PageUtils page = cjroneWelfareMatterApplicationService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 镇街道审核
     */
    @RequestMapping("/audit")
    // @RequiresPermissions("matter_application:disabilitycertificateapplication:update")
    public R audit(@RequestBody CjroneWelfareMatterApplicationEntity cjroneWelfareMatterApplication){
        // 根据id获得惠残事项的详细信息
        CjroneWelfareMatterApplicationEntity entity = cjroneWelfareMatterApplicationService.getById(cjroneWelfareMatterApplication.getId());
        System.out.println(new Gson().toJson(entity));
        if (entity != null){
            if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("镇街道") != -1){
                // 判断是否达到了审核的条件
                // 生活补贴的审核条件与其他的不同
                if("生活补贴".equals(entity.getMatterName())){
                    if("4".equals(entity.getSignStatus())){  //民政待手签，同时也表示了街道已手签
                        if("4".equals(entity.getSignatureStatus())){
                            System.out.println("aaaaaaa");
                        }else{
                            return R.error().put("code",100).put("msg","审核未通过，镇街道未电子公章！").put("applyId",cjroneWelfareMatterApplication.getId());
                        }
                    }else{
                        return R.error().put("code",100).put("msg","审核未通过，镇街道未手签！").put("applyId",cjroneWelfareMatterApplication.getId());
                    }
                }else{
                    if("6".equals(entity.getSignStatus())){  //区残联待手签
                        if("5".equals(entity.getSignatureStatus())){  // 区残联待电子盖章
                            System.out.println("aaaaaaa");
                        }else{
                            return R.error().put("code",100).put("msg","审核未通过，镇街道未电子公章！").put("applyId",cjroneWelfareMatterApplication.getId());
                        }
                    }else{
                        return R.error().put("code",100).put("msg","审核未通过，镇街道未手签！").put("applyId",cjroneWelfareMatterApplication.getId());
                    }
                }
            }
        }


        //更新对应的惠残事项表
        if (entity != null){
            if("生活补贴".equals(entity.getMatterName())){
                if("1".equals(cjroneWelfareMatterApplication.getStatus())){
                    //审核通过
                    cjroneWelfareMatterApplication.setStatus("4"); //民政待审核

                    CjroneblLivingSubsidyEntity cjroneblLivingSubsidyEntity=new CjroneblLivingSubsidyEntity();
                    cjroneblLivingSubsidyEntity.setId(entity.getMatterId());
                    cjroneblLivingSubsidyEntity.setStatus("4"); //民政待审核
                    cjroneblLivingSubsidyService.updateById(cjroneblLivingSubsidyEntity);
                }
                if("0".equals(cjroneWelfareMatterApplication.getStatus())){
                    // 审核退回
                    String statusOptions=cjroneWelfareMatterApplication.getStatusOptions();
                    cjroneWelfareMatterApplication.setStatus("9"); //街道退回至社区

                    CjroneblLivingSubsidyEntity cjroneblLivingSubsidyEntity=new CjroneblLivingSubsidyEntity();
                    cjroneblLivingSubsidyEntity.setId(entity.getMatterId());
                    cjroneblLivingSubsidyEntity.setStatusOptions(cjroneWelfareMatterApplication.getStatusOptions());
                    cjroneblLivingSubsidyEntity.setStatus("9"); //街道退回至社区
                    cjroneblLivingSubsidyService.updateById(cjroneblLivingSubsidyEntity);
                }
            }
            else if("护理补贴".equals(entity.getMatterName())){
                if("1".equals(cjroneWelfareMatterApplication.getStatus())){
                    //审核通过
                    cjroneWelfareMatterApplication.setStatus("6"); //区残联负责人待审核

                    CjroneblNursingSubsidyEntity cjroneblNursingSubsidyEntity=new CjroneblNursingSubsidyEntity();
                    cjroneblNursingSubsidyEntity.setId(entity.getMatterId());
                    cjroneblNursingSubsidyEntity.setStatus("6"); //区残联负责人待审核
                    cjroneblNursingSubsidyService.updateById(cjroneblNursingSubsidyEntity);
                }
                if("0".equals(cjroneWelfareMatterApplication.getStatus())){
                    // 审核退回
                    String statusOptions=cjroneWelfareMatterApplication.getStatusOptions();
                    cjroneWelfareMatterApplication.setStatus("9"); //街道退回至社区

                    CjroneblNursingSubsidyEntity cjroneblNursingSubsidyEntity=new CjroneblNursingSubsidyEntity();
                    cjroneblNursingSubsidyEntity.setId(entity.getMatterId());
                    cjroneblNursingSubsidyEntity.setStatusOptions(cjroneWelfareMatterApplication.getStatusOptions());
                    cjroneblNursingSubsidyEntity.setStatus("9"); //街道退回至社区
                    cjroneblNursingSubsidyService.updateById(cjroneblNursingSubsidyEntity);
                }
            }
            else if("生活补助金".equals(entity.getMatterName())){
                if("1".equals(cjroneWelfareMatterApplication.getStatus())){
                    //审核通过
                    cjroneWelfareMatterApplication.setStatus("6"); //区残联经办人待审核

                    CjroneblLivingAllowanceEntity cjroneblLivingAllowanceEntity=new CjroneblLivingAllowanceEntity();
                    cjroneblLivingAllowanceEntity.setId(entity.getMatterId());
                    cjroneblLivingAllowanceEntity.setStatus("6"); //区残联经办人待审核
                    cjroneblLivingAllowanceService.updateById(cjroneblLivingAllowanceEntity);
                }
                if("0".equals(cjroneWelfareMatterApplication.getStatus())){
                    // 审核退回
                    String statusOptions=cjroneWelfareMatterApplication.getStatusOptions();
                    cjroneWelfareMatterApplication.setStatus("9"); //街道退回至社区

                    CjroneblLivingAllowanceEntity cjroneblLivingAllowanceEntity=new CjroneblLivingAllowanceEntity();
                    cjroneblLivingAllowanceEntity.setId(entity.getMatterId());
                    cjroneblLivingAllowanceEntity.setStatusOptions(cjroneWelfareMatterApplication.getStatusOptions());
                    cjroneblLivingAllowanceEntity.setStatus("9"); //街道退回至社区
                    cjroneblLivingAllowanceService.updateById(cjroneblLivingAllowanceEntity);
                }
            }
            else if("职工基本养老保险补助".equals(entity.getMatterName())){
                if("1".equals(cjroneWelfareMatterApplication.getStatus())){
                    //审核通过
                    cjroneWelfareMatterApplication.setStatus("6"); //区残联经办人待审核

                    CjroneblZgjbyanglaoEntity cjroneblZgjbyanglaoEntity=new CjroneblZgjbyanglaoEntity();
                    cjroneblZgjbyanglaoEntity.setId(entity.getMatterId());
                    cjroneblZgjbyanglaoEntity.setStatus("6"); //区残联经办人待审核
                    cjroneblZgjbyanglaoService.updateById(cjroneblZgjbyanglaoEntity);
                }
                if("0".equals(cjroneWelfareMatterApplication.getStatus())){
                    // 审核退回
                    String statusOptions=cjroneWelfareMatterApplication.getStatusOptions();
                    cjroneWelfareMatterApplication.setStatus("9"); //街道退回至社区

                    CjroneblZgjbyanglaoEntity cjroneblZgjbyanglaoEntity=new CjroneblZgjbyanglaoEntity();
                    cjroneblZgjbyanglaoEntity.setId(entity.getMatterId());
                    cjroneblZgjbyanglaoEntity.setStatusOptions(cjroneWelfareMatterApplication.getStatusOptions());
                    cjroneblZgjbyanglaoEntity.setStatus("9"); //街道退回至社区
                    cjroneblZgjbyanglaoService.updateById(cjroneblZgjbyanglaoEntity);
                }
            }
            else if("职工基本医疗保险补助".equals(entity.getMatterName())){
                if("1".equals(cjroneWelfareMatterApplication.getStatus())){
                    //审核通过
                    cjroneWelfareMatterApplication.setStatus("6"); //区残联经办人待审核

                    CjroneblZgjbyiliaoEntity cjroneblZgjbyiliaoEntity=new CjroneblZgjbyiliaoEntity();
                    cjroneblZgjbyiliaoEntity.setId(entity.getMatterId());
                    cjroneblZgjbyiliaoEntity.setStatus("6"); //区残联经办人待审核
                    cjroneblZgjbyiliaoService.updateById(cjroneblZgjbyiliaoEntity);
                }
                if("0".equals(cjroneWelfareMatterApplication.getStatus())){
                    // 审核退回
                    String statusOptions=cjroneWelfareMatterApplication.getStatusOptions();
                    cjroneWelfareMatterApplication.setStatus("9"); //街道退回至社区

                    CjroneblZgjbyiliaoEntity cjroneblZgjbyiliaoEntity=new CjroneblZgjbyiliaoEntity();
                    cjroneblZgjbyiliaoEntity.setId(entity.getMatterId());
                    cjroneblZgjbyiliaoEntity.setStatusOptions(cjroneWelfareMatterApplication.getStatusOptions());
                    cjroneblZgjbyiliaoEntity.setStatus("9"); //街道退回至社区
                    cjroneblZgjbyiliaoService.updateById(cjroneblZgjbyiliaoEntity);
                }
            }
            else if("城乡居民养老保险补助".equals(entity.getMatterName())){
                if("1".equals(cjroneWelfareMatterApplication.getStatus())){
                    //审核通过
                    cjroneWelfareMatterApplication.setStatus("6"); //区残联经办人待审核

                    CjroneblCxjmyanglaoEntity cjroneblCxjmyanglaoEntity=new CjroneblCxjmyanglaoEntity();
                    cjroneblCxjmyanglaoEntity.setId(entity.getMatterId());
                    cjroneblCxjmyanglaoEntity.setStatus("6"); //区残联经办人待审核
                    cjroneblCxjmyanglaoService.updateById(cjroneblCxjmyanglaoEntity);
                }
                if("0".equals(cjroneWelfareMatterApplication.getStatus())){
                    // 审核退回
                    String statusOptions=cjroneWelfareMatterApplication.getStatusOptions();
                    cjroneWelfareMatterApplication.setStatus("9"); //街道退回至社区

                    CjroneblCxjmyanglaoEntity cjroneblCxjmyanglaoEntity=new CjroneblCxjmyanglaoEntity();
                    cjroneblCxjmyanglaoEntity.setId(entity.getMatterId());
                    cjroneblCxjmyanglaoEntity.setStatusOptions(cjroneWelfareMatterApplication.getStatusOptions());
                    cjroneblCxjmyanglaoEntity.setStatus("9"); //街道退回至社区
                    cjroneblCxjmyanglaoService.updateById(cjroneblCxjmyanglaoEntity);
                }
            }
            else if("城乡基本医疗保险补助".equals(entity.getMatterName())){
                if("1".equals(cjroneWelfareMatterApplication.getStatus())){
                    //审核通过
                    cjroneWelfareMatterApplication.setStatus("6"); //区残联经办人待审核

                    CjroneblCxjmyiliaoEntity cjroneblCxjmyiliaoEntity=new CjroneblCxjmyiliaoEntity();
                    cjroneblCxjmyiliaoEntity.setId(entity.getMatterId());
                    cjroneblCxjmyiliaoEntity.setStatus("6"); //区残联经办人待审核
                    cjroneblCxjmyiliaoService.updateById(cjroneblCxjmyiliaoEntity);
                }
                if("0".equals(cjroneWelfareMatterApplication.getStatus())){
                    // 审核退回
                    String statusOptions=cjroneWelfareMatterApplication.getStatusOptions();
                    cjroneWelfareMatterApplication.setStatus("9"); //街道退回至社区

                    CjroneblCxjmyiliaoEntity cjroneblCxjmyiliaoEntity=new CjroneblCxjmyiliaoEntity();
                    cjroneblCxjmyiliaoEntity.setId(entity.getMatterId());
                    cjroneblCxjmyiliaoEntity.setStatusOptions(cjroneWelfareMatterApplication.getStatusOptions());
                    cjroneblCxjmyiliaoEntity.setStatus("9"); //街道退回至社区
                    cjroneblCxjmyiliaoService.updateById(cjroneblCxjmyiliaoEntity);
                }
            }
            else if("残疾人临时救助".equals(entity.getMatterName())){
                if("1".equals(cjroneWelfareMatterApplication.getStatus())){
                    //审核通过
                    cjroneWelfareMatterApplication.setStatus("6"); //区残联经办人待审核

                    CjroneblTemporaryAssistanceEntity cjroneblTemporaryAssistanceEntity=new CjroneblTemporaryAssistanceEntity();
                    cjroneblTemporaryAssistanceEntity.setId(entity.getMatterId());
                    cjroneblTemporaryAssistanceEntity.setStatus("6"); //区残联经办人待审核
                    cjroneblTemporaryAssistanceService.updateById(cjroneblTemporaryAssistanceEntity);
                }
                if("0".equals(cjroneWelfareMatterApplication.getStatus())){
                    // 审核退回
                    String statusOptions=cjroneWelfareMatterApplication.getStatusOptions();
                    cjroneWelfareMatterApplication.setStatus("9"); //街道退回至社区

                    CjroneblTemporaryAssistanceEntity cjroneblTemporaryAssistanceEntity=new CjroneblTemporaryAssistanceEntity();
                    cjroneblTemporaryAssistanceEntity.setId(entity.getMatterId());
                    cjroneblTemporaryAssistanceEntity.setStatusOptions(cjroneWelfareMatterApplication.getStatusOptions());
                    cjroneblTemporaryAssistanceEntity.setStatus("9"); //街道退回至社区
                    cjroneblTemporaryAssistanceService.updateById(cjroneblTemporaryAssistanceEntity);
                }
            }
            else if("康复补助".equals(entity.getMatterName())){
                if("1".equals(cjroneWelfareMatterApplication.getStatus())){
                    //审核通过
                    cjroneWelfareMatterApplication.setStatus("6"); //区残联经办人待审核

                    CjroneblRehabilitationSubsidyEntity cjroneblRehabilitationSubsidyEntity=new CjroneblRehabilitationSubsidyEntity();
                    cjroneblRehabilitationSubsidyEntity.setId(entity.getMatterId());
                    cjroneblRehabilitationSubsidyEntity.setStatus("6"); //区残联经办人待审核
                    cjroneblRehabilitationSubsidyService.updateById(cjroneblRehabilitationSubsidyEntity);
                }
                if("0".equals(cjroneWelfareMatterApplication.getStatus())){
                    // 审核退回
                    String statusOptions=cjroneWelfareMatterApplication.getStatusOptions();
                    cjroneWelfareMatterApplication.setStatus("9"); //街道退回至社区

                    CjroneblRehabilitationSubsidyEntity cjroneblRehabilitationSubsidyEntity=new CjroneblRehabilitationSubsidyEntity();
                    cjroneblRehabilitationSubsidyEntity.setId(entity.getMatterId());
                    cjroneblRehabilitationSubsidyEntity.setStatusOptions(cjroneWelfareMatterApplication.getStatusOptions());
                    cjroneblRehabilitationSubsidyEntity.setStatus("9"); //街道退回至社区
                    cjroneblRehabilitationSubsidyService.updateById(cjroneblRehabilitationSubsidyEntity);
                }
            }
            else if("创业补助".equals(entity.getMatterName())){
                if("1".equals(cjroneWelfareMatterApplication.getStatus())){
                    //审核通过
                    cjroneWelfareMatterApplication.setStatus("6"); //区残联经办人待审核

                    CjroneblBusinessGrantEntity cjroneblBusinessGrantEntity=new CjroneblBusinessGrantEntity();
                    cjroneblBusinessGrantEntity.setId(entity.getMatterId());
                    cjroneblBusinessGrantEntity.setStatus("6"); //区残联经办人待审核
                    cjroneblBusinessGrantService.updateById(cjroneblBusinessGrantEntity);
                }
                if("0".equals(cjroneWelfareMatterApplication.getStatus())){
                    // 审核退回
                    String statusOptions=cjroneWelfareMatterApplication.getStatusOptions();
                    cjroneWelfareMatterApplication.setStatus("9"); //街道退回至社区

                    CjroneblBusinessGrantEntity cjroneblBusinessGrantEntity=new CjroneblBusinessGrantEntity();
                    cjroneblBusinessGrantEntity.setId(entity.getMatterId());
                    cjroneblBusinessGrantEntity.setStatusOptions(cjroneWelfareMatterApplication.getStatusOptions());
                    cjroneblBusinessGrantEntity.setStatus("9"); //街道退回至社区
                    cjroneblBusinessGrantService.updateById(cjroneblBusinessGrantEntity);
                }
            }
            else if("残疾人子女教育补贴".equals(entity.getMatterName())){
                if("1".equals(cjroneWelfareMatterApplication.getStatus())){
                    //审核通过
                    cjroneWelfareMatterApplication.setStatus("6"); //区残联经办人待审核

                    CjroneblChildeduEntity cjroneblChildeduEntity=new CjroneblChildeduEntity();
                    cjroneblChildeduEntity.setId(entity.getMatterId());
                    cjroneblChildeduEntity.setStatus("6"); //区残联经办人待审核
                    cjroneblChildeduService.updateById(cjroneblChildeduEntity);
                }
                if("0".equals(cjroneWelfareMatterApplication.getStatus())){
                    // 审核退回
                    String statusOptions=cjroneWelfareMatterApplication.getStatusOptions();
                    cjroneWelfareMatterApplication.setStatus("9"); //街道退回至社区

                    CjroneblChildeduEntity cjroneblChildeduEntity=new CjroneblChildeduEntity();
                    cjroneblChildeduEntity.setStatusOptions(cjroneWelfareMatterApplication.getStatusOptions());
                    cjroneblChildeduEntity.setStatus("9"); //街道退回至社区
                    cjroneblChildeduService.updateById(cjroneblChildeduEntity);
                }
            }
            else if("医疗救助".equals(entity.getMatterName())){
                if("1".equals(cjroneWelfareMatterApplication.getStatus())){
                    //审核通过
                    cjroneWelfareMatterApplication.setStatus("6"); //区残联经办人待审核

                    CjroneblMedicalSupportEntity cjroneblMedicalSupportEntity=new CjroneblMedicalSupportEntity();
                    cjroneblMedicalSupportEntity.setId(entity.getMatterId());
                    cjroneblMedicalSupportEntity.setStatus("6"); //区残联经办人待审核
                    cjroneblMedicalSupportService.updateById(cjroneblMedicalSupportEntity);
                }
                if("0".equals(cjroneWelfareMatterApplication.getStatus())){
                    // 审核退回
                    String statusOptions=cjroneWelfareMatterApplication.getStatusOptions();
                    cjroneWelfareMatterApplication.setStatus("9"); //街道退回至社区

                    CjroneblMedicalSupportEntity cjroneblMedicalSupportEntity=new CjroneblMedicalSupportEntity();
                    cjroneblMedicalSupportEntity.setId(entity.getMatterId());
                    cjroneblMedicalSupportEntity.setStatusOptions(cjroneWelfareMatterApplication.getStatusOptions());
                    cjroneblMedicalSupportEntity.setStatus("9"); //街道退回至社区
                    cjroneblMedicalSupportService.updateById(cjroneblMedicalSupportEntity);
                }
            }
            else if("住院补助".equals(entity.getMatterName())){
                if("1".equals(cjroneWelfareMatterApplication.getStatus())){
                    //审核通过
                    cjroneWelfareMatterApplication.setStatus("6"); //区残联经办人待审核

                    CjroneblHospitalizationAllowanceEntity cjroneblHospitalizationAllowanceEntity=new CjroneblHospitalizationAllowanceEntity();
                    cjroneblHospitalizationAllowanceEntity.setId(entity.getMatterId());
                    cjroneblHospitalizationAllowanceEntity.setStatus("6"); //区残联经办人待审核
                    cjroneblHospitalizationAllowanceService.updateById(cjroneblHospitalizationAllowanceEntity);
                }
                if("0".equals(cjroneWelfareMatterApplication.getStatus())){
                    // 审核退回
                    String statusOptions=cjroneWelfareMatterApplication.getStatusOptions();
                    cjroneWelfareMatterApplication.setStatus("9"); //街道退回至社区

                    CjroneblHospitalizationAllowanceEntity cjroneblHospitalizationAllowanceEntity=new CjroneblHospitalizationAllowanceEntity();
                    cjroneblHospitalizationAllowanceEntity.setId(entity.getMatterId());
                    cjroneblHospitalizationAllowanceEntity.setStatusOptions(cjroneWelfareMatterApplication.getStatusOptions());
                    cjroneblHospitalizationAllowanceEntity.setStatus("9"); //街道退回至社区
                    cjroneblHospitalizationAllowanceService.updateById(cjroneblHospitalizationAllowanceEntity);
                }
            }
            else if("智慧爱心24小时".equals(entity.getMatterName())){
                if("1".equals(cjroneWelfareMatterApplication.getStatus())){
                    //审核通过
                    cjroneWelfareMatterApplication.setStatus("6"); //区残联经办人待审核

                    Love24Entity love24Entity=new Love24Entity();
                    love24Entity.setId(entity.getMatterId());
                    love24Entity.setStatus("6"); //区残联经办人待审核
                    love24Service.updateById(love24Entity);
                }
                if("0".equals(cjroneWelfareMatterApplication.getStatus())){
                    // 审核退回
                    String statusOptions=cjroneWelfareMatterApplication.getStatusOptions();
                    cjroneWelfareMatterApplication.setStatus("9"); //街道退回至社区

                    Love24Entity love24Entity=new Love24Entity();
                    love24Entity.setStatusOptions(cjroneWelfareMatterApplication.getStatusOptions());
                    love24Entity.setStatus("9"); //街道退回至社区
                    love24Service.updateById(love24Entity);
                }
            }

        }


        cjroneWelfareMatterApplicationService.updateStatusById(cjroneWelfareMatterApplication);



        return R.ok().put("applyId",cjroneWelfareMatterApplication.getId());
    }


    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("matter_application:cjronewelfarematterapplication:delete")
    public R delete(@RequestBody Integer[] ids){
        Arrays.asList(ids).forEach(item ->{
            CjroneWelfareMatterApplicationEntity  welfareMatterApplicationEntity = cjroneWelfareMatterApplicationService.getById(item);
            if (welfareMatterApplicationEntity !=  null && "生活补助金".equals(welfareMatterApplicationEntity.getMatterName())){
                cjroneblLivingAllowanceService.removeById(welfareMatterApplicationEntity.getMatterId());
            }else if (welfareMatterApplicationEntity !=  null && "护理补贴".equals(welfareMatterApplicationEntity.getMatterName())){
                cjroneblNursingSubsidyService.removeById(welfareMatterApplicationEntity.getMatterId());
            }else if (welfareMatterApplicationEntity !=  null && "生活补贴".equals(welfareMatterApplicationEntity.getMatterName())){
                cjroneblLivingSubsidyService.removeById(welfareMatterApplicationEntity.getMatterId());
            }else if (welfareMatterApplicationEntity !=  null && "职工基本养老保险补助".equals(welfareMatterApplicationEntity.getMatterName())){
                cjroneblZgjbyanglaoService.removeById(welfareMatterApplicationEntity.getMatterId());
            }
            else if (welfareMatterApplicationEntity !=  null && "城乡居民养老保险补助".equals(welfareMatterApplicationEntity.getMatterName())){
                cjroneblCxjmyanglaoService.removeById(welfareMatterApplicationEntity.getMatterId());
            }
            else if (welfareMatterApplicationEntity !=  null && "职工基本医疗保险补助".equals(welfareMatterApplicationEntity.getMatterName())){
                cjroneblZgjbyiliaoService.removeById(welfareMatterApplicationEntity.getMatterId());
            }
            else if (welfareMatterApplicationEntity !=  null && "城乡基本医疗保险补助".equals(welfareMatterApplicationEntity.getMatterName())){
                cjroneblCxjmyiliaoService.removeById(welfareMatterApplicationEntity.getMatterId());
            }
            else if (welfareMatterApplicationEntity !=  null && "残疾人临时救助".equals(welfareMatterApplicationEntity.getMatterName())){
                cjroneblTemporaryAssistanceService.removeById(welfareMatterApplicationEntity.getMatterId());
            }
            else if (welfareMatterApplicationEntity !=  null && "康复补助".equals(welfareMatterApplicationEntity.getMatterName())){
                cjroneblRehabilitationSubsidyService.removeById(welfareMatterApplicationEntity.getMatterId());
            }
            else if (welfareMatterApplicationEntity !=  null && "创业补助".equals(welfareMatterApplicationEntity.getMatterName())){
                cjroneblBusinessGrantService.removeById(welfareMatterApplicationEntity.getMatterId());
            }
            else if (welfareMatterApplicationEntity !=  null && "残疾人子女教育补贴".equals(welfareMatterApplicationEntity.getMatterName())){
                cjroneblChildeduService.removeById(welfareMatterApplicationEntity.getMatterId());
            }
            else if (welfareMatterApplicationEntity !=  null && "医疗救助".equals(welfareMatterApplicationEntity.getMatterName())){
                cjroneblMedicalSupportService.removeById(welfareMatterApplicationEntity.getMatterId());
            }
            else if (welfareMatterApplicationEntity !=  null && "住院补助".equals(welfareMatterApplicationEntity.getMatterName())){
                cjroneblHospitalizationAllowanceService.removeById(welfareMatterApplicationEntity.getMatterId());
            }
            if(welfareMatterApplicationEntity !=  null && "智慧爱心24小时".equals(welfareMatterApplicationEntity.getMatterName())){
                love24Service.removeById(welfareMatterApplicationEntity.getMatterId());
            }
            if (welfareMatterApplicationEntity != null && "残疾少年儿童康复训练补助".equals(welfareMatterApplicationEntity.getMatterName())) {
                cjroneChildrenRehabilitationSubsidyService.removeById(welfareMatterApplicationEntity.getMatterId());
            }
        });
        cjroneWelfareMatterApplicationService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }



    //</editor-fold>

    // 以下是之前的代码

    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("matter_application:cjronewelfarematterapplication:info")
    public R info(@PathVariable("id") Integer id){
		CjroneWelfareMatterApplicationEntity cjroneWelfareMatterApplication = cjroneWelfareMatterApplicationService.getById(id);

        return R.ok().put("cjroneWelfareMatterApplication", cjroneWelfareMatterApplication);
    }



    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("matter_application:cjronewelfarematterapplication:update")
    public R update(@RequestBody CjroneWelfareMatterApplicationEntity cjroneWelfareMatterApplication){
		cjroneWelfareMatterApplicationService.updateById(cjroneWelfareMatterApplication);

        return R.ok();
    }
    /**
     * 禁用
     */
    @RequestMapping("/enable")
    public R enable(@RequestBody CjroneWelfareMatterApplicationEntity cjroneWelfareMatterApplication){
		cjroneWelfareMatterApplicationService.enable(cjroneWelfareMatterApplication);

        return R.ok();
    }
    /**

    /**
     /**
     * 区残联审核
     */
    @RequestMapping("/auditQcl")
    // @RequiresPermissions("matter_application:disabilitycertificateapplication:update")
    public R auditQcl(@RequestBody CjroneWelfareMatterApplicationEntity cjroneWelfareMatterApplication){
        cjroneWelfareMatterApplicationService.updateStatusById(cjroneWelfareMatterApplication);
        return R.ok().put("applyId",cjroneWelfareMatterApplication.getId());
    }

    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    // @RequiresPermissions("matter_application:cjronewelfarematterapplication:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }
        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<CjroneWelfareMatterApplicationEntity> cjroneWelfareMatterApplicationList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get("主键ID"));
                    item.put("name",item.get("残疾人姓名"));
                    item.put("idCard",item.get("身份证号"));
                    item.put("mobilePhone",item.get("手机号"));
                    item.put("disableId",item.get("残疾证号"));
                    item.put("matterName",item.get("事项名称"));
                    item.put("applicationTime",item.get("申请时间"));
                    item.put("verifyTime",item.get("审核时间"));
                    item.put("matterId",item.get("事项ID"));
                    cjroneWelfareMatterApplicationList.add(new Gson().fromJson(new Gson().toJson(item), CjroneWelfareMatterApplicationEntity.class));
        });
        // 保存到数据库
        cjroneWelfareMatterApplicationService.saveBatch(cjroneWelfareMatterApplicationList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportDataOld")
    // @RequiresPermissions("matter_application:cjronewelfarematterapplication:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        mapArgs.put("zhen",getUser().getZhen());
        List<CjroneWelfareMatterApplicationEntity> cjroneWelfareMatterApplicationEntityList = cjroneWelfareMatterApplicationService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("", null, "");
        Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneWelfareMatterApplicationEntity.class, cjroneWelfareMatterApplicationEntityList);

        response.setContentType("application/vnd.ms-excel");
        // String fileName = "惠残事项" + mapArgs.get("matterName") != null && !"".equals(mapArgs.get("matterName")) ? mapArgs.get("matterName").toString() : "";
        String fileName = "惠残事项";
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    // @RequiresPermissions("matter_application:cjronewelfarematterapplication:export")
    public void exportDataNew(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        if(!"智慧爱心24小时".equals(mapArgs.get("matterName"))){
            mapArgs.put("zhen",getUser().getZhen());
        }else{
            mapArgs.put("cun",getUser().getCun());
        }
        List<CjroneWelfareMatterApplicationEntity> cjroneWelfareMatterApplicationEntityList = cjroneWelfareMatterApplicationService.queryExportData(mapArgs);
        if (mapArgs.get("matterName") != null && "就业创业补助".equals(mapArgs.get("matterName"))){
            List<Integer> ids = cjroneWelfareMatterApplicationEntityList.stream().map(x->x.getMatterId()).collect(Collectors.toList()); //获得id的集合
            //cjroneWelfareMatterApplicationEntityList.forEach(item->item.getMatterId());
            List<CjroneEmploymentSubsidyEntity> cjroneEmploymentSubsidyEntityList = (List<CjroneEmploymentSubsidyEntity>) cjroneEmploymentSubsidyService.listByIds(ids);
            //对残疾类别进行处理
            cjroneEmploymentSubsidyEntityList.stream().forEach(item -> {
                if(item.getDisabilityType()==1){
                    item.setDisabilityTypeName("视力");
                }else if(item.getDisabilityType()==2){
                    item.setDisabilityTypeName("听力");
                }else if(item.getDisabilityType()==3){
                    item.setDisabilityTypeName("智力");
                }else if(item.getDisabilityType()==4){
                    item.setDisabilityTypeName("精神");
                }else if(item.getDisabilityType()==5){
                    item.setDisabilityTypeName("肢体");
                }else if(item.getDisabilityType()==6){
                    item.setDisabilityTypeName("言语");
                }else if(item.getDisabilityType()==7){
                    item.setDisabilityTypeName("肢体");
                }

                if(item.getSex()==1){
                    item.setSexName("男");
                }else{
                    item.setSexName("女");
                }

            });

            ExportParams params = new ExportParams("就业创业补助", null, "就业创业补助");
            Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneEmploymentSubsidyEntity.class, cjroneEmploymentSubsidyEntityList);

            response.setContentType("application/vnd.ms-excel");
            String fileName = "就业创业补助" ;
            response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
            OutputStream ouputStream = response.getOutputStream();
            workbook.write(ouputStream);
            ouputStream.flush();
            ouputStream.close();

        }else  if (mapArgs.get("matterName") != null && "康复补助".equals(mapArgs.get("matterName"))){
            List<Integer> ids = cjroneWelfareMatterApplicationEntityList.stream().map(x->x.getMatterId()).collect(Collectors.toList()); //获得id的集合
            List<CjroneRehabilitationSubsidyEntity> cjroneRehabilitationSubsidyEntityList = (List<CjroneRehabilitationSubsidyEntity>)cjroneRehabilitationSubsidyService.listByIds(ids);

            ExportParams params = new ExportParams("康复补助申请", null, "康复补助申请");
            Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneRehabilitationSubsidyEntity.class, cjroneRehabilitationSubsidyEntityList);

            response.setContentType("application/vnd.ms-excel");
            String fileName = "康复补助申请" ;
            response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
            OutputStream ouputStream = response.getOutputStream();
            workbook.write(ouputStream);
            ouputStream.flush();
            ouputStream.close();
        } else  if (mapArgs.get("matterName") != null && "智慧爱心24小时".equals(mapArgs.get("matterName"))){
            List<Integer> ids = cjroneWelfareMatterApplicationEntityList.stream().map(x->x.getMatterId()).collect(Collectors.toList()); //获得id的集合
            List<Love24Entity> love24EntityList =new ArrayList<>();
            if(ids.size()>0){
                love24EntityList = (List<Love24Entity>)love24Service.listByIds(ids);
            }
            List<Love24DXEntity> dxEntities = new ArrayList<>();
            List<Love24YDEntity> ydEntities = new ArrayList<>();



            if(mapArgs.get("role") == null){
                ExportParams params = new ExportParams("智慧爱心24小时", null, "智慧爱心24小时");
                Workbook workbook =   ExcelExportUtil.exportExcel(params, Love24Entity.class, love24EntityList);
                response.setContentType("application/vnd.ms-excel");
                String fileName = "智慧爱心24小时" ;

                response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
                OutputStream ouputStream = response.getOutputStream();
                workbook.write(ouputStream);
                ouputStream.flush();
                ouputStream.close();

            }
            if( mapArgs.get("role").toString().indexOf("移动") != -1){

                System.out.println("是否移动："+(mapArgs.get("role").toString().indexOf("移动") != -1));
                ydEntities= ConvertUtils.sourceToTarget(love24EntityList,Love24YDEntity.class);

                ydEntities.stream().forEach(item ->{

                    if("1".equals(item.getSex())){
                        item.setSexName("男");
                    }else{
                        item.setSexName("女");
                    }


                    if("1".equals(item.getDisableId())){
                        item.setIsDisableName("是");
                    }else{
                        item.setIsDisableName("否");
                    }

                    String [] typeId = item.getType().split(",");
                    String typeName = "";
                    for (int i = 0; i < typeId.length; i++) {
                        if("10".equals(typeId[i])){
                            typeName = typeName + "智能机手机定位";
                        }else if("11".equals(typeId[i])){
                            typeName = typeName + "购机优惠";

                        }else if("12".equals(typeId[i])){
                            typeName = typeName + "套餐优惠";

                        }else if("13".equals(typeId[i])){
                            typeName = typeName + "宽带电视";

                        }

                        if(i< typeId.length-1){
                            typeName = typeName +",";
                        }
                        System.out.println("申请类型："+typeName);
                    }
                    item.setTypeName(typeName);
                });
                ExportParams params = new ExportParams("智慧爱心24小时", null, "智慧爱心24小时");
                Workbook workbook =   ExcelExportUtil.exportExcel(params, Love24YDEntity.class, ydEntities);

                response.setContentType("application/vnd.ms-excel");
                String fileName = "智慧爱心24小时" ;

                response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
                OutputStream ouputStream = response.getOutputStream();
                workbook.write(ouputStream);
                ouputStream.flush();
                ouputStream.close();
            }

            if(mapArgs.get("role").toString().indexOf("电信") != -1){
                System.out.println("是否电信："+(mapArgs.get("role").toString().indexOf("电信") != -1));

                dxEntities=  ConvertUtils.sourceToTarget(love24EntityList,Love24DXEntity.class);

                dxEntities.stream().forEach(item ->{

                    if("1".equals(item.getSex())){
                        item.setSexName("男");
                    }else{
                        item.setSexName("女");
                    }


                    if("1".equals(item.getDisableId())){
                        item.setIsDisableName("是");
                    }else{
                        item.setIsDisableName("否");
                    }

                    String [] typeId = item.getType().split(",");
                    String typeName = "";
                    for (int i = 0; i < typeId.length; i++) {
                        if("0".equals(typeId[i])){
                            typeName = typeName + "固定电话+定制手机";
                        }else if("1".equals(typeId[i])){
                            typeName = typeName + "定制CDMA手机一部";
                        }else if("2".equals(typeId[i])){
                            typeName = typeName + "固定电话";
                        }else if("3".equals(typeId[i])){
                            typeName = typeName + "号码定位";
                        }else if("4".equals(typeId[i])){
                            typeName = typeName + "单手机用户";
                        }

                        if(i< typeId.length-1){
                            typeName += ",";
                        }

                    }
                    item.setTypeName(typeName);
                });

                ExportParams params = new ExportParams("智慧爱心24小时", null, "智慧爱心24小时");
                Workbook workbook = ExcelExportUtil.exportExcel(params, Love24DXEntity.class, dxEntities);

                response.setContentType("application/vnd.ms-excel");
                String fileName = "智慧爱心24小时" ;
                response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
                OutputStream ouputStream = response.getOutputStream();
                workbook.write(ouputStream);
                ouputStream.flush();
                ouputStream.close();
            }

        }
        else  if (mapArgs.get("matterName") != null && "护理补贴".equals(mapArgs.get("matterName"))){
            List<Integer> ids = cjroneWelfareMatterApplicationEntityList.stream().map(x->x.getMatterId()).collect(Collectors.toList()); //获得id的集合
            List<CjroneNursingSubsidyEntity> cjroneNursingSubsidyEntityList = (List<CjroneNursingSubsidyEntity>)cjroneNursingSubsidyService.listByIds(ids);
            cjroneNursingSubsidyEntityList.forEach(item ->{
                if ("1".equals(item.getDisabilityCategory())){
                    item.setDisabilityCategory("视力");
                }else  if ("2".equals(item.getDisabilityCategory())){
                    item.setDisabilityCategory("听力");
                }else  if ("3".equals(item.getDisabilityCategory())){
                    item.setDisabilityCategory("智力");
                }else  if ("4".equals(item.getDisabilityCategory())){
                    item.setDisabilityCategory("精神");
                }else  if ("5".equals(item.getDisabilityCategory())){
                    item.setDisabilityCategory("肢体");
                }else  if ("6".equals(item.getDisabilityCategory())){
                    item.setDisabilityCategory("言语");
                }else  if ("7".equals(item.getDisabilityCategory())){
                    item.setDisabilityCategory("肢体");
                }
                if ("1".equals(item.getDisabilityDegree())){
                    item.setDisabilityDegree("一级");
                }else  if ("2".equals(item.getDisabilityDegree())){
                    item.setDisabilityDegree("二级");
                }else  if ("3".equals(item.getDisabilityDegree())){
                    item.setDisabilityDegree("三级");
                }else  if ("4".equals(item.getDisabilityDegree())){
                    item.setDisabilityDegree("四级");
                }

                //开始处理性别
                if(item.getSex()==1){
                    item.setSexName("男");
                }else{
                    item.setSexName("女");
                }

                if(item.getCareType()!=null){
                    String aaa[] = item.getCareType().substring(1, item.getCareType().length() - 1).split(",");
                    String careTypeName = null;
                    for (String s : aaa) {
                        if (s.replace("\"", "").equals("1")) {
                            if (careTypeName == null) {
                                careTypeName = "居家安养 ";
                            } else {
                                careTypeName = careTypeName + "居家安养 ";
                            }
                        } else if (s.replace("\"", "").equals("2")) {
                            if (careTypeName == null) {
                                careTypeName = "日间照料 ";
                            } else {
                                careTypeName = careTypeName + "日间照料 ";
                            }
                        } else if (s.replace("\"", "").equals("3")) {
                            if (careTypeName == null) {
                                careTypeName = "集中托养 ";
                            } else {
                                careTypeName = careTypeName + "集中托养 ";
                            }
                        } else if (s.replace("\"", "").equals("4")) {
                            if (careTypeName == null) {
                                careTypeName = "项目服务 ";
                            } else {
                                careTypeName = careTypeName + "项目服务 ";
                            }
                        }
                    }
                    item.setCareType(careTypeName);
                }
                else{
                    item.setCareType("无");
                }

                //开始处理籍贯数据
                DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity = disabilityCertificateApplicationService.getByIDCard(item.getIdCard());
                if(disabilityCertificateApplicationEntity!=null){
                    item.setNativePlace(disabilityCertificateApplicationEntity.getNativeZhenName()+disabilityCertificateApplicationEntity.getNativeCunName());
                }
            });

            ExportParams params = new ExportParams("护理补贴申请", null, "护理补贴申请");
            Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneNursingSubsidyEntity.class, cjroneNursingSubsidyEntityList);

            response.setContentType("application/vnd.ms-excel");
            String fileName = "护理补贴申请" ;
            response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
            OutputStream ouputStream = response.getOutputStream();
            workbook.write(ouputStream);
            ouputStream.flush();
            ouputStream.close();
        } else  if (mapArgs.get("matterName") != null && "生活补贴".equals(mapArgs.get("matterName"))){
            List<Integer> ids = cjroneWelfareMatterApplicationEntityList.stream().map(x->x.getMatterId()).collect(Collectors.toList()); //获得id的集合
            List<CjroneLivingAllowanceEntity> cjroneLivingAllowanceEntityList = (List<CjroneLivingAllowanceEntity>)cjroneLivingAllowanceService.listByIds(ids);

            cjroneLivingAllowanceEntityList.forEach(item ->{

                //开始处理性别
                if(item.getSex()==1){
                    item.setSexName("男");
                }else{
                    item.setSexName("女");
                }

                //开始处理籍贯数据
                DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity = disabilityCertificateApplicationService.getByIDCard(item.getIdCard());
                if(disabilityCertificateApplicationEntity!=null){
                    item.setNativePlace(disabilityCertificateApplicationEntity.getNativeZhenName()+disabilityCertificateApplicationEntity.getNativeCunName());
                }
            });

            ExportParams params = new ExportParams("生活补贴申请", null, "生活补贴申请");
            Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneLivingAllowanceEntity.class, cjroneLivingAllowanceEntityList);

            response.setContentType("application/vnd.ms-excel");
            String fileName = "生活补贴申请" ;
            response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
            OutputStream ouputStream = response.getOutputStream();
            workbook.write(ouputStream);
            ouputStream.flush();
            ouputStream.close();
        }else  if (mapArgs.get("matterName") != null && "个体工商户养老补贴".equals(mapArgs.get("matterName"))){
            List<Integer> ids = cjroneWelfareMatterApplicationEntityList.stream().map(x->x.getMatterId()).collect(Collectors.toList()); //获得id的集合
            List<CjroneResidentPensionInsuranceEntity> cjroneResidentPensionInsuranceEntityList = (List<CjroneResidentPensionInsuranceEntity>)cjroneResidentPensionInsuranceService.listByIds(ids);

            ExportParams params = new ExportParams("个体工商户保险补贴汇总表", null, "个体工商户保险补贴汇总表");
            Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneResidentPensionInsuranceEntity.class, cjroneResidentPensionInsuranceEntityList);

            response.setContentType("application/vnd.ms-excel");
            String fileName = "个体工商户保险补贴汇总表" ;
            System.out.println("export fileName --->"+fileName);
            response.setHeader("Content-disposition", "attachment;filename="+new String("hcashdsa".getBytes(),"iso-8859-1")+".xls");
            System.out.println("Content-disposition-->"+response.getHeader("Content-disposition"));
            OutputStream ouputStream = response.getOutputStream();
            workbook.write(ouputStream);
            ouputStream.flush();
            ouputStream.close();
        }else  if (mapArgs.get("matterName") != null && "城乡居民养老补贴".equals(mapArgs.get("matterName"))){
            List<Integer> ids = cjroneWelfareMatterApplicationEntityList.stream().map(x->x.getMatterId()).collect(Collectors.toList()); //获得id的集合
            List<DataResidentPensionInsuranceEntity> dataResidentPensionInsuranceEntityList = (List<DataResidentPensionInsuranceEntity>)dataResidentPensionInsuranceService.listByIds(ids);

            ExportParams params = new ExportParams("城乡居民养老保险补贴汇总表", null, "城乡居民养老保险补贴汇总表");
            Workbook workbook = ExcelExportUtil.exportExcel(params, DataResidentPensionInsuranceEntity.class, dataResidentPensionInsuranceEntityList);

            response.setContentType("application/vnd.ms-excel");
            String fileName = "城乡居民养老保险补贴汇总表" ;
            response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
            OutputStream ouputStream = response.getOutputStream();
            workbook.write(ouputStream);
            ouputStream.flush();
            ouputStream.close();
        }else  if (mapArgs.get("matterName") != null && "基础段残疾人子女教育补贴".equals(mapArgs.get("matterName"))){
            List<Integer> ids = cjroneWelfareMatterApplicationEntityList.stream().map(x->x.getMatterId()).collect(Collectors.toList()); //获得id的集合
            List<DataChildEducationSubsidyEntity> dataChildEducationSubsidyEntityList = (List<DataChildEducationSubsidyEntity>)dataChildEducationSubsidyService.listByIds(ids);

            dataChildEducationSubsidyEntityList.forEach(item ->{
                //开始处理性别
                if("1".equals(item.getSex())){
                    item.setSex("男");
                }else{
                    item.setSex("女");
                }
            });

            ExportParams params = new ExportParams("残疾人子女教育补贴导入表", null, "残疾人子女教育补贴导入表");
            Workbook workbook = ExcelExportUtil.exportExcel(params, DataChildEducationSubsidyEntity.class, dataChildEducationSubsidyEntityList);

            response.setContentType("application/vnd.ms-excel");
            String fileName = "残疾人子女教育补贴导入表" ;
            response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
            OutputStream ouputStream = response.getOutputStream();
            workbook.write(ouputStream);
            ouputStream.flush();
            ouputStream.close();
        }else  if (mapArgs.get("matterName") != null && "综合医疗保险".equals(mapArgs.get("matterName"))){
            List<Integer> ids = cjroneWelfareMatterApplicationEntityList.stream().map(x->x.getMatterId()).collect(Collectors.toList()); //获得id的集合
            List<CjroneComprehensiveMedicalInsuranceEntity> cjroneComprehensiveMedicalInsuranceEntityList = (List<CjroneComprehensiveMedicalInsuranceEntity>)cjroneComprehensiveMedicalInsuranceService.listByIds(ids);

            ExportParams params = new ExportParams("残疾人综合医疗保险", null, "残疾人综合医疗保险");
            Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneComprehensiveMedicalInsuranceEntity.class, cjroneComprehensiveMedicalInsuranceEntityList);

            response.setContentType("application/vnd.ms-excel");
            String fileName = "残疾人综合医疗保险" ;
            response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
            OutputStream ouputStream = response.getOutputStream();
            workbook.write(ouputStream);
            ouputStream.flush();
            ouputStream.close();
        }else  if (mapArgs.get("matterName") != null && "家庭无障碍改造".equals(mapArgs.get("matterName"))){
            List<Integer> ids = cjroneWelfareMatterApplicationEntityList.stream().map(x->x.getMatterId()).collect(Collectors.toList()); //获得id的集合
            List<CjroneFamilyAccessibilityTransformationEntity> cjroneFamilyAccessibilityTransformationEntityList = (List<CjroneFamilyAccessibilityTransformationEntity>)cjroneFamilyAccessibilityTransformationService.listByIds(ids);

            ExportParams params = new ExportParams("家庭无障碍改造", null, "家庭无障碍改造");
            Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneFamilyAccessibilityTransformationEntity.class, cjroneFamilyAccessibilityTransformationEntityList);

            response.setContentType("application/vnd.ms-excel");
            String fileName = "家庭无障碍改造" ;
            response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
            OutputStream ouputStream = response.getOutputStream();
            workbook.write(ouputStream);
            ouputStream.flush();
            ouputStream.close();

        } else {
            ExportParams params = new ExportParams("", null, "");
            Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneWelfareMatterApplicationEntity.class, cjroneWelfareMatterApplicationEntityList);

            response.setContentType("application/vnd.ms-excel");
            // String fileName = "惠残事项" + mapArgs.get("matterName") != null && !"".equals(mapArgs.get("matterName")) ? mapArgs.get("matterName").toString() : "";
            String fileName = "惠残事项";
            response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
            OutputStream ouputStream = response.getOutputStream();
            workbook.write(ouputStream);
            ouputStream.flush();
            ouputStream.close();
        }
    }
    /**
     * 获取康复补助下拉数据
     */
    @RequestMapping("/queryKfbzOptions")
    // @RequiresPermissions("sys:sysdict:list")
    public R queryDataByMap(@RequestParam Map<String, Object> params){
        /*List<DataRehabilitationSubsidyCategoryEntity> rsList= dataRehabilitationSubsidyCategoryService.getRehabilitationSubsidyCategoryList(null);
        List<Map<String,Object>> res= new ArrayList<>();

        rsList.forEach(item ->{
            String aaa = new Gson().toJson(item);
            aaa = aaa.replaceAll("categoryId","value");
            aaa = aaa.replaceAll("name","label");
            aaa = aaa.replaceAll("list","children");
            res.add(new Gson().fromJson(aaa,Map.class));
        });*/

        List<DataRehabilitationSubsidyCategoryEntity> rsList= dataRehabilitationSubsidyCategoryService.getRehabilitationSubsidyCategoryList(null);
        return R.ok().put("list", rsList);
    }

    //计算年龄
    public static int IdNOToAge(String IdNO){
        int leh = IdNO.length();
        String dates="";
        if (leh == 18) {
            dates = IdNO.substring(6, 10);
            SimpleDateFormat df = new SimpleDateFormat("yyyy");
            String year=df.format(new Date());
            int u=Integer.parseInt(year)-Integer.parseInt(dates);
            return u;
        }else{
            dates = IdNO.substring(6, 8);
            return Integer.parseInt(dates);
        }
    }

    /**
      * 生成电子签章 pdf
      */
    @RequestMapping("/printPDF/{id}")
    public R printPDF(@PathVariable("id") Integer id) throws UnsupportedEncodingException {
        CjroneWelfareMatterApplicationEntity cjroneWelfareMatterApplication = cjroneWelfareMatterApplicationService.getById(id);
        String fileUrl = null;
        String fileName = null;
        if ("生活补贴".equals(cjroneWelfareMatterApplication.getMatterName())){
            Map<String, Object> tmp_params = new HashMap<>();
            tmp_params.put("type","惠残事项生活补贴");
            tmp_params.put("status","1");
            tmp_params.put("type_id",cjroneWelfareMatterApplication.getMatterId());
            List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
            if (alive_list.size()>0) {
                CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
                fileUrl =  cjroneSignatureEntity.getUrl();
                fileName = cjroneSignatureEntity.getFileName();
            }
        }else if ("护理补贴".equals(cjroneWelfareMatterApplication.getMatterName())){
            Map<String, Object> tmp_params = new HashMap<>();
            tmp_params.put("type","惠残事项护理补贴");
            tmp_params.put("status","1");
            tmp_params.put("type_id",cjroneWelfareMatterApplication.getMatterId());
            List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
            if (alive_list.size()>0) {
                CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
                fileUrl =  cjroneSignatureEntity.getUrl();
                fileName = cjroneSignatureEntity.getFileName();
            }
        }else if ("康复补助".equals(cjroneWelfareMatterApplication.getMatterName())){
            Map<String, Object> tmp_params = new HashMap<>();
            tmp_params.put("type","惠残事项康复补助");
            tmp_params.put("status","1");
            tmp_params.put("type_id",cjroneWelfareMatterApplication.getMatterId());
            List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
            if (alive_list.size()>0){
                CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
                fileUrl =  cjroneSignatureEntity.getUrl();
                fileName = cjroneSignatureEntity.getFileName();
            }
        }
        return R.ok().put("fileUrl", fileUrl).put("fileName", fileName);
    }

}
